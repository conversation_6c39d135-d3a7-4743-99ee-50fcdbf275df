﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using PEMTestSystem.Data;
using PEMTestSystem.Services;
using Serilog;
using System;
using System.Configuration;
using System.IO;
using System.Windows;
using System.Windows.Threading;

namespace PEMTestSystem
{
    public partial class App : Application
    {
        private IHost? _host;

        // 添加静态属性以便全局访问AlarmService
        public static AlarmService AlarmService { get; private set; } = null!;

        // 添加Host属性以便其他类访问服务
        public IHost? Host => _host;

        protected override async void OnStartup(StartupEventArgs e)
        {
            try
            {
                // 创建主机配置
                _host = Microsoft.Extensions.Hosting.Host.CreateDefaultBuilder()
                    .ConfigureServices((context, services) =>
                    {
                        ConfigureServices(services);
                    })
                    .ConfigureLogging(logging =>
                    {
                        ConfigureLogging(logging);
                    })
                    .Build();

                // 启动主机
                await _host.StartAsync();

                // 初始化AlarmService静态属性
                AlarmService = _host.Services.GetRequiredService<AlarmService>();

                // 初始化数据库
                using (var scope = _host.Services.CreateScope())
                {
                    var initializer = scope.ServiceProvider.GetRequiredService<DatabaseInitializer>();
                    await initializer.InitializeAsync();
                }

                // 显示主窗口
                var mainWindow = _host.Services.GetRequiredService<MainWindow>();
                mainWindow.Show();

                // 设置主窗口为应用程序的主窗口
                MainWindow = mainWindow;

                // 不调用 base.OnStartup(e)，或者在调用前清除 StartupUri
                // base.OnStartup(e);

            }
            catch (Exception ex)
            {
                MessageBox.Show($"应用程序启动失败: {ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                Shutdown();
            }
        }

        protected override async void OnExit(ExitEventArgs e)
        {
            try
            {
                // 设置应用程序关闭标志，防止在关闭过程中显示消息框
                AlarmService?.SetApplicationShuttingDown();

                if (_host != null)
                {
                    await _host.StopAsync();
                    _host.Dispose();
                }
            }
            catch (Exception ex)
            {
                // 在应用程序退出时，即使发生异常也要继续退出过程
                // 只记录到控制台，避免使用可能已经失效的日志系统
                System.Console.WriteLine($"应用程序退出时发生异常: {ex.Message}");
            }
            finally
            {
                base.OnExit(e);
            }
        }

        private void ConfigureServices(IServiceCollection services)
        {
            // 获取连接字符串
            var connectionString = ConfigurationManager.ConnectionStrings["DefaultConnection"]?.ConnectionString;
            if (string.IsNullOrEmpty(connectionString))
            {
                throw new InvalidOperationException("未找到数据库连接字符串");
            }

            // 配置数据库
            services.AddDbContext<PEMTestDbContext>(options =>
            {
                options.UseSqlite(connectionString);
                options.EnableSensitiveDataLogging(false);
                options.EnableServiceProviderCaching();
            });

            // 配置 Serilog
            var logPath = GetStringSetting("LogFilePath", "Logs\\PEMTestSystem.log");
            EnsureDirectoryExists(Path.GetDirectoryName(logPath));
            
            var logger = new LoggerConfiguration()
                .MinimumLevel.Debug()
                .WriteTo.Console()
                .WriteTo.File(logPath, rollingInterval: RollingInterval.Day)
                .CreateLogger();

            // 注册 Serilog ILogger
            services.AddSingleton<Serilog.ILogger>(logger);

            // 注册服务
            services.AddScoped<DatabaseInitializer>();
            services.AddScoped<AlarmService>();

            // 注册配置服务
            services.AddSingleton<IConfigurationService, ConfigurationService>();
            services.AddScoped<DeviceConfigurationService>();

            // 注册设备服务
            services.AddSingleton<Services.Devices.SerialPortManager>();
            services.AddSingleton<Services.Devices.DeviceManager>();

            // 注册数据采集服务
            services.AddSingleton<DataAcquisitionService>(provider =>
            {
                var serviceProvider = provider;
                var configService = provider.GetRequiredService<DeviceConfigurationService>();
                var dispatcher = System.Windows.Application.Current?.Dispatcher ?? Dispatcher.CurrentDispatcher;
                return new DataAcquisitionService(serviceProvider, configService, dispatcher);
            });
            services.AddHostedService<DataAcquisitionService>(provider => provider.GetRequiredService<DataAcquisitionService>());

            // 注册窗口
            services.AddTransient<MainWindow>();
        }

        private void ConfigureLogging(ILoggingBuilder builder)
        {
            var logLevel = GetLogLevel();
            builder.SetMinimumLevel(logLevel);

            // 控制台日志
            if (GetBooleanSetting("EnableConsoleLogging", true))
            {
                builder.AddConsole();
            }

            // 调试输出
            builder.AddDebug();

            // 文件日志（如果需要的话）
            if (GetBooleanSetting("EnableFileLogging", true))
            {
                var logPath = GetStringSetting("LogFilePath", "Logs\\PEMTestSystem.log");
                EnsureDirectoryExists(Path.GetDirectoryName(logPath));
                // 这里可以添加文件日志提供程序
            }
        }

        private LogLevel GetLogLevel()
        {
            var logLevelString = GetStringSetting("LogLevel", "Information");
            return Enum.TryParse<LogLevel>(logLevelString, out var level) ? level : LogLevel.Information;
        }

        private string GetStringSetting(string key, string defaultValue = "")
        {
            return ConfigurationManager.AppSettings[key] ?? defaultValue;
        }

        private bool GetBooleanSetting(string key, bool defaultValue = false)
        {
            var value = ConfigurationManager.AppSettings[key];
            return bool.TryParse(value, out var result) ? result : defaultValue;
        }

        private double GetDoubleSetting(string key, double defaultValue = 0.0)
        {
            var value = ConfigurationManager.AppSettings[key];
            return double.TryParse(value, out var result) ? result : defaultValue;
        }

        private int GetIntSetting(string key, int defaultValue = 0)
        {
            var value = ConfigurationManager.AppSettings[key];
            return int.TryParse(value, out var result) ? result : defaultValue;
        }

        private void EnsureDirectoryExists(string? directoryPath)
        {
            if (!string.IsNullOrEmpty(directoryPath) && !Directory.Exists(directoryPath))
            {
                Directory.CreateDirectory(directoryPath);
            }
        }
    }
}

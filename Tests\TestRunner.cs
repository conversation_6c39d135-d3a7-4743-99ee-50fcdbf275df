using System;
using System.Threading.Tasks;

namespace PEMTestSystem.Tests
{
    /// <summary>
    /// 测试运行器
    /// </summary>
    public class TestRunner
    {
        /// <summary>
        /// 运行设备禁用/启用功能测试
        /// </summary>
        public static async Task<int> Main(string[] args)
        {
            Console.WriteLine("PEM电解槽自动化测试系统 - 设备禁用/启用功能测试");
            Console.WriteLine("=".PadRight(60, '='));
            Console.WriteLine();

            try
            {
                var tests = new DeviceEnableDisableTests();
                var allTestsPassed = await tests.RunAllTests();
                
                tests.Dispose();
                
                if (allTestsPassed)
                {
                    Console.WriteLine("🎉 所有测试通过！设备禁用/启用功能正常工作。");
                    return 0;
                }
                else
                {
                    Console.WriteLine("❌ 部分测试失败，请检查实现。");
                    return 1;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试运行异常: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
                return 1;
            }
        }
    }
}

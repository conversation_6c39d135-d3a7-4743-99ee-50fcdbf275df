using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using PEMTestSystem.Data;
using PEMTestSystem.Models.Configuration;
using PEMTestSystem.Models.Core;
using PEMTestSystem.Services.Devices;
using System.Collections.Concurrent;
using System.Windows.Threading;

namespace PEMTestSystem.Services
{
    /// <summary>
    /// 数据采集服务
    /// 负责从设备采集数据并存储到数据库
    /// </summary>
    public class DataAcquisitionService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;

        private readonly DeviceConfigurationService _configService;
        private readonly Dispatcher _uiDispatcher;
        
        // 数据缓存
        private readonly ConcurrentQueue<DataPoint> _dataQueue = new();
        private readonly List<DataPoint> _batchBuffer = new();
        private readonly object _batchLock = new object();
        
        // 配置参数
        private DataAcquisitionSettings _settings = new();
        private DeviceManager? _deviceManager;
        
        // 定时器
        private Timer? _acquisitionTimer;
        private Timer? _batchInsertTimer;
        
        // 状态
        private bool _isRunning = false;
        private Guid? _currentExperimentId;
        private DateTime _experimentStartTime;
        
        // 事件
        public event EventHandler<DataPointEventArgs>? DataPointReceived;
        public event EventHandler<AcquisitionStatusEventArgs>? StatusChanged;

        public DataAcquisitionService(
            IServiceProvider serviceProvider,
            DeviceConfigurationService configService,
            Dispatcher uiDispatcher)
        {
            _serviceProvider = serviceProvider;
            _configService = configService;
            _uiDispatcher = uiDispatcher;
        }

        /// <summary>
        /// 启动数据采集
        /// </summary>
        public async Task<bool> StartAcquisitionAsync(Guid experimentId)
        {
            try
            {
                if (_isRunning)
                {
                    App.AlarmService.Warning("数据采集", "数据采集已在运行中");
                    return false;
                }

                _currentExperimentId = experimentId;
                _experimentStartTime = DateTime.Now;

                // 加载配置
                var config = await _configService.GetConfigurationAsync();
                _settings = config.DataAcquisition;

                // 初始化设备管理器
                _deviceManager = _serviceProvider.GetRequiredService<DeviceManager>();
                await _deviceManager.LoadDeviceConfigurationsAsync();
                await _deviceManager.InitializeAllDevicesAsync();

                // 订阅电源设备数据变化事件
                SubscribeToPowerSupplyEvents();

                // 启动采集定时器
                var acquisitionInterval = TimeSpan.FromSeconds(_settings.SamplingIntervalSeconds);
                _acquisitionTimer = new Timer(AcquisitionTimerCallback, null, TimeSpan.Zero, acquisitionInterval);

                // 如果需要批量插入，启动批量插入定时器
                if (_settings.SamplingIntervalSeconds < _settings.BatchInsertThresholdSeconds)
                {
                    var batchInterval = TimeSpan.FromSeconds(_settings.BatchInsertThresholdSeconds);
                    _batchInsertTimer = new Timer(BatchInsertTimerCallback, null, batchInterval, batchInterval);
                    App.AlarmService.Info("数据采集", $"启用批量插入模式，批量间隔: {_settings.BatchInsertThresholdSeconds}秒");
                }

                _isRunning = true;
                App.AlarmService.Info("数据采集", $"数据采集已启动，采样间隔: {_settings.SamplingIntervalSeconds}秒");
                
                OnStatusChanged(AcquisitionStatus.Running, "数据采集已启动");
                return true;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("数据采集", "启动数据采集失败", ex);
                OnStatusChanged(AcquisitionStatus.Error, $"启动失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 停止数据采集
        /// </summary>
        public async Task<bool> StopAcquisitionAsync()
        {
            try
            {
                if (!_isRunning)
                {
                    return true;
                }

                _isRunning = false;

                // 停止定时器
                _acquisitionTimer?.Dispose();
                _batchInsertTimer?.Dispose();

                // 处理剩余的缓存数据
                await FlushRemainingDataAsync();

                // 取消订阅电源设备事件
                UnsubscribeFromPowerSupplyEvents();

                _currentExperimentId = null;
                App.AlarmService.Info("数据采集", "数据采集已停止");
                
                OnStatusChanged(AcquisitionStatus.Stopped, "数据采集已停止");
                return true;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("数据采集", "停止数据采集失败", ex);
                OnStatusChanged(AcquisitionStatus.Error, $"停止失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 数据采集定时器回调
        /// </summary>
        private async void AcquisitionTimerCallback(object? state)
        {
            if (!_isRunning || _deviceManager == null || !_currentExperimentId.HasValue)
                return;

            try
            {
                var dataPoint = await CollectDataPointAsync();
                if (dataPoint != null)
                {
                    // 添加到队列
                    _dataQueue.Enqueue(dataPoint);

                    // 如果不使用批量插入，立即保存
                    if (_settings.SamplingIntervalSeconds >= _settings.BatchInsertThresholdSeconds)
                    {
                        await SaveDataPointAsync(dataPoint);
                    }

                    // 通知UI
                    OnDataPointReceived(dataPoint);

                    // 检查内存限制
                    if (_dataQueue.Count > _settings.MaxDataPointsInMemory)
                    {
                        App.AlarmService.Warning("数据采集", "内存中数据点数量超过限制，强制批量保存");
                        await ForceBatchInsertAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("数据采集", "数据采集异常", ex);
            }
        }

        /// <summary>
        /// 批量插入定时器回调
        /// </summary>
        private async void BatchInsertTimerCallback(object? state)
        {
            if (!_isRunning)
                return;

            try
            {
                await ForceBatchInsertAsync();
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("数据采集", "批量插入异常", ex);
            }
        }

        /// <summary>
        /// 采集单个数据点
        /// </summary>
        private async Task<DataPoint?> CollectDataPointAsync()
        {
            try
            {
                if (_deviceManager == null || !_currentExperimentId.HasValue)
                    return null;

                var now = DateTime.Now;
                var elapsedSeconds = (decimal)(now - _experimentStartTime).TotalSeconds;

                // 获取温控器数据
                var tempController = _deviceManager.GetTemperatureController("TempController_Main");
                var temperature = 0m;
                var isTempControllerEnabled = await _deviceManager.IsDeviceEnabledAsync("TempController_Main");
                if (tempController != null && tempController.IsConnected && isTempControllerEnabled)
                {
                    try
                    {
                        temperature = (decimal)tempController.CurrentTemperature;
                    }
                    catch (Exception ex)
                    {
                        App.AlarmService.Warning("数据采集", $"获取温控器数据失败: {ex.Message}");
                        temperature = 25m; // 使用默认室温值
                    }
                }
                else if (!isTempControllerEnabled)
                {
                    temperature = 25m; // 设备禁用时使用默认室温值
                    App.AlarmService.Debug("数据采集", "温控器已禁用，使用默认温度值");
                }

                // 获取流量泵1数据
                var pump1 = _deviceManager.GetFlowPump("Pump_01");
                var flowRate1 = 0m;
                var isPump1Enabled = await _deviceManager.IsDeviceEnabledAsync("Pump_01");
                if (pump1 != null && pump1.IsConnected && isPump1Enabled)
                {
                    try
                    {
                        flowRate1 = (decimal)pump1.CurrentFlowRate;
                    }
                    catch (Exception ex)
                    {
                        App.AlarmService.Warning("数据采集", $"获取流量泵1数据失败: {ex.Message}");
                    }
                }
                else if (!isPump1Enabled)
                {
                    App.AlarmService.Debug("数据采集", "流量泵1已禁用，流量值为0");
                }

                // 获取流量泵2数据
                var pump2 = _deviceManager.GetFlowPump("Pump_02");
                var flowRate2 = 0m;
                var isPump2Enabled = await _deviceManager.IsDeviceEnabledAsync("Pump_02");
                if (pump2 != null && pump2.IsConnected && isPump2Enabled)
                {
                    try
                    {
                        flowRate2 = (decimal)pump2.CurrentFlowRate;
                    }
                    catch (Exception ex)
                    {
                        App.AlarmService.Warning("数据采集", $"获取流量泵2数据失败: {ex.Message}");
                    }
                }
                else if (!isPump2Enabled)
                {
                    App.AlarmService.Debug("数据采集", "流量泵2已禁用，流量值为0");
                }

                // 获取电源数据（电压、电流）
                var voltage = 0m;
                var current = 0m;
                var powerSupply = _deviceManager.GetPowerSupply("PowerSupply_Main");
                var isPowerSupplyEnabled = await _deviceManager.IsDeviceEnabledAsync("PowerSupply_Main");

                if (powerSupply != null && powerSupply.IsConnected && isPowerSupplyEnabled)
                {
                    try
                    {
                        // 并行获取电压和电流数据以提高效率
                        var voltageTask = powerSupply.GetVoltageAsync();
                        var currentTask = powerSupply.GetCurrentAsync();

                        await Task.WhenAll(voltageTask, currentTask);

                        voltage = (decimal)voltageTask.Result;
                        current = (decimal)currentTask.Result;
                    }
                    catch (Exception ex)
                    {
                        App.AlarmService.Warning("数据采集", $"获取电源数据失败，使用上次数值: {ex.Message}");
                        // 使用电源设备的当前缓存值
                        voltage = (decimal)powerSupply.CurrentVoltage;
                        current = (decimal)powerSupply.CurrentCurrent;
                    }
                }
                else if (!isPowerSupplyEnabled)
                {
                    // 电源已禁用时使用零值
                    voltage = 0m;
                    current = 0m;
                    App.AlarmService.Debug("数据采集", "电源设备已禁用，电压电流值为0");
                }
                else
                {
                    // 电源未连接时使用模拟数据
                    voltage = 1.8m + (decimal)(0.1 * Math.Sin((double)elapsedSeconds * 0.1));
                    current = 20m + (decimal)(2 * Math.Sin((double)elapsedSeconds * 0.05));

                    if (powerSupply == null)
                    {
                        App.AlarmService.Warning("数据采集", "电源设备未找到，使用模拟数据");
                    }
                    else
                    {
                        App.AlarmService.Warning("数据采集", "电源设备未连接，使用模拟数据");
                    }
                }

                var dataPoint = new DataPoint
                {
                    ExperimentId = _currentExperimentId.Value,
                    Timestamp = now,
                    ElapsedSeconds = elapsedSeconds,
                    Voltage = voltage,
                    Current = current,
                    Temperature = temperature,
                    FlowRate1 = flowRate1,
                    FlowRate2 = flowRate2,
                    Quality = DataQuality.Normal
                };

                return dataPoint;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("数据采集", "采集数据点失败", ex);
                return null;
            }
        }

        /// <summary>
        /// 保存单个数据点
        /// </summary>
        private async Task SaveDataPointAsync(DataPoint dataPoint)
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<PEMTestDbContext>();
                
                context.DataPoints.Add(dataPoint);
                await context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("数据采集", "保存数据点失败", ex);
                throw;
            }
        }

        /// <summary>
        /// 强制批量插入
        /// </summary>
        private async Task ForceBatchInsertAsync()
        {
            var dataPoints = new List<DataPoint>();

            // 从队列中取出数据
            while (_dataQueue.TryDequeue(out var dataPoint) && dataPoints.Count < _settings.BatchSize)
            {
                dataPoints.Add(dataPoint);
            }

            if (dataPoints.Count > 0)
            {
                await BatchInsertDataPointsAsync(dataPoints);
                App.AlarmService.Debug("数据采集", $"批量插入 {dataPoints.Count} 个数据点");
            }
        }

        /// <summary>
        /// 批量插入数据点
        /// </summary>
        private async Task BatchInsertDataPointsAsync(List<DataPoint> dataPoints)
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<PEMTestDbContext>();
                
                context.DataPoints.AddRange(dataPoints);
                await context.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("数据采集", "批量插入数据点失败", ex);
                throw;
            }
        }

        /// <summary>
        /// 刷新剩余数据
        /// </summary>
        private async Task FlushRemainingDataAsync()
        {
            try
            {
                var remainingData = new List<DataPoint>();
                while (_dataQueue.TryDequeue(out var dataPoint))
                {
                    remainingData.Add(dataPoint);
                }

                if (remainingData.Count > 0)
                {
                    await BatchInsertDataPointsAsync(remainingData);
                    App.AlarmService.Info("数据采集", $"刷新剩余 {remainingData.Count} 个数据点");
                }
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("数据采集", "刷新剩余数据失败", ex);
            }
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            // 后台服务主循环
            while (!stoppingToken.IsCancellationRequested)
            {
                await Task.Delay(1000, stoppingToken);
            }
        }

        public override async Task StopAsync(CancellationToken cancellationToken)
        {
            await StopAcquisitionAsync();
            await base.StopAsync(cancellationToken);
        }

        private void OnDataPointReceived(DataPoint dataPoint)
        {
            _uiDispatcher.BeginInvoke(() =>
            {
                DataPointReceived?.Invoke(this, new DataPointEventArgs(dataPoint));
            });
        }

        private void OnStatusChanged(AcquisitionStatus status, string message)
        {
            _uiDispatcher.BeginInvoke(() =>
            {
                StatusChanged?.Invoke(this, new AcquisitionStatusEventArgs(status, message));
            });
        }

        /// <summary>
        /// 订阅电源设备数据变化事件
        /// </summary>
        private void SubscribeToPowerSupplyEvents()
        {
            try
            {
                var powerSupply = _deviceManager?.GetPowerSupply("PowerSupply_Main");
                if (powerSupply != null)
                {
                    powerSupply.DataChanged += OnPowerSupplyDataChanged;
                    App.AlarmService.Info("数据采集", "已订阅电源设备数据变化事件");
                }
                else
                {
                    App.AlarmService.Warning("数据采集", "电源设备未找到，无法订阅数据变化事件");
                }
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("数据采集", "订阅电源设备事件失败", ex);
            }
        }

        /// <summary>
        /// 取消订阅电源设备数据变化事件
        /// </summary>
        private void UnsubscribeFromPowerSupplyEvents()
        {
            try
            {
                var powerSupply = _deviceManager?.GetPowerSupply("PowerSupply_Main");
                if (powerSupply != null)
                {
                    powerSupply.DataChanged -= OnPowerSupplyDataChanged;
                    App.AlarmService.Info("数据采集", "已取消订阅电源设备数据变化事件");
                }
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("数据采集", "取消订阅电源设备事件失败", ex);
            }
        }

        /// <summary>
        /// 电源设备数据变化事件处理
        /// </summary>
        private void OnPowerSupplyDataChanged(object? sender, PowerSupplyDataChangedEventArgs e)
        {
            try
            {
                // 这里可以处理电源设备的实时数据变化
                // 例如：更新缓存值、触发特殊事件、检查安全阈值等

                App.AlarmService.Debug("数据采集",
                    $"电源数据更新 - 电压: {e.Voltage:F2}V, 电流: {e.Current:F2}A, 功率: {e.Power:F2}W, 输出: {(e.IsOutputEnabled ? "开启" : "关闭")}");

                // 如果需要，可以在这里触发额外的数据处理逻辑
                // 例如：安全检查、数据验证、实时报警等
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("数据采集", "处理电源设备数据变化事件失败", ex);
            }
        }

        public override void Dispose()
        {
            _acquisitionTimer?.Dispose();
            _batchInsertTimer?.Dispose();

            // 取消订阅电源设备事件
            UnsubscribeFromPowerSupplyEvents();

            base.Dispose();
        }
    }

    /// <summary>
    /// 数据点事件参数
    /// </summary>
    public class DataPointEventArgs : EventArgs
    {
        public DataPoint DataPoint { get; }

        public DataPointEventArgs(DataPoint dataPoint)
        {
            DataPoint = dataPoint;
        }
    }

    /// <summary>
    /// 采集状态事件参数
    /// </summary>
    public class AcquisitionStatusEventArgs : EventArgs
    {
        public AcquisitionStatus Status { get; }
        public string Message { get; }

        public AcquisitionStatusEventArgs(AcquisitionStatus status, string message)
        {
            Status = status;
            Message = message;
        }
    }

    /// <summary>
    /// 采集状态枚举
    /// </summary>
    public enum AcquisitionStatus
    {
        Stopped,
        Running,
        Error,
        Paused
    }
}

using Microsoft.EntityFrameworkCore;
using PEMTestSystem.Models.Core;
using PEMTestSystem.Models.Devices;
using PEMTestSystem.Models.Control;
using PEMTestSystem.Models.System;
using PEMTestSystem.Models.Security;
using PEMTestSystem.Models.Export;
using PEMTestSystem.Models;
using System;

namespace PEMTestSystem.Data
{
    /// <summary>
    /// PEM测试系统数据库上下文
    /// </summary>
    public class PEMTestDbContext : DbContext
    {
        public PEMTestDbContext(DbContextOptions<PEMTestDbContext> options) : base(options)
        {
        }

        // 核心业务表
        public DbSet<Experiment> Experiments { get; set; } = null!;
        public DbSet<DataPoint> DataPoints { get; set; } = null!;
        public DbSet<ExperimentTemplate> ExperimentTemplates { get; set; } = null!;

        // 设备管理表
        public DbSet<Device> Devices { get; set; } = null!;
        public DbSet<PEMTestSystem.Models.Devices.DeviceStatus> DeviceStatuses { get; set; } = null!;

        // 实验控制表
        public DbSet<ExperimentState> ExperimentStates { get; set; } = null!;
        public DbSet<ParameterChange> ParameterChanges { get; set; } = null!;

        // 系统管理表
        public DbSet<SystemLog> SystemLogs { get; set; } = null!;
        public DbSet<SystemConfiguration> SystemConfigurations { get; set; } = null!;

        // 安全和审计表
        public DbSet<SecurityEvent> SecurityEvents { get; set; } = null!;

        // 数据导出表
        public DbSet<DataExport> DataExports { get; set; } = null!;

        // 警告记录表
        public DbSet<AlarmRecord> AlarmRecords { get; set; } = null!;

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // 配置索引
            ConfigureIndexes(modelBuilder);

            // 配置关系
            ConfigureRelationships(modelBuilder);

            // 配置约束
            ConfigureConstraints(modelBuilder);

            // 种子数据
            ConfigureSeedData(modelBuilder);
        }

        private void ConfigureIndexes(ModelBuilder modelBuilder)
        {
            // Experiments 索引
            modelBuilder.Entity<Experiment>()
                .HasIndex(e => new { e.ExperimentType, e.Status })
                .HasDatabaseName("IX_Experiments_Type_Status");

            modelBuilder.Entity<Experiment>()
                .HasIndex(e => e.ExperimentStartTime)
                .HasDatabaseName("IX_Experiments_StartTime");

            // DataPoints 索引 - SQLite兼容版本
            modelBuilder.Entity<DataPoint>()
                .HasIndex(dp => new { dp.ExperimentId, dp.Timestamp })
                .HasDatabaseName("IX_DataPoints_ExperimentId_Timestamp");

            modelBuilder.Entity<DataPoint>()
                .HasIndex(dp => new { dp.ExperimentId, dp.ElapsedSeconds })
                .HasDatabaseName("IX_DataPoints_ExperimentId_ElapsedSeconds");

            // 添加数据质量索引用于数据分析
            modelBuilder.Entity<DataPoint>()
                .HasIndex(dp => new { dp.Quality, dp.Timestamp })
                .HasDatabaseName("IX_DataPoints_Quality_Timestamp");

            // ExperimentTemplates 索引
            modelBuilder.Entity<ExperimentTemplate>()
                .HasIndex(et => new { et.ExperimentType, et.IsDefault })
                .HasDatabaseName("IX_ExperimentTemplates_Type_Default");

            modelBuilder.Entity<ExperimentTemplate>()
                .HasIndex(et => et.Name)
                .HasDatabaseName("IX_ExperimentTemplates_Name");

            // Devices 索引
            modelBuilder.Entity<Device>()
                .HasIndex(d => d.DeviceId)
                .IsUnique()
                .HasDatabaseName("UX_Devices_DeviceId");

            // AlarmRecords 索引优化
            modelBuilder.Entity<AlarmRecord>()
                .HasIndex(ar => new { ar.Level, ar.CreateTime })
                .IncludeProperties(ar => new { ar.Type, ar.IsConfirmed })
                .HasDatabaseName("IX_AlarmRecords_Level_CreateTime_Covering");

            modelBuilder.Entity<Device>()
                .HasIndex(d => d.DeviceType)
                .HasDatabaseName("IX_Devices_Type");

            // DeviceStatus 索引
            modelBuilder.Entity<PEMTestSystem.Models.Devices.DeviceStatus>()
                .HasIndex(ds => new { ds.DeviceId, ds.Timestamp })
                .HasDatabaseName("IX_DeviceStatus_DeviceId_Timestamp");

            // SystemConfiguration 索引
            modelBuilder.Entity<SystemConfiguration>()
                .HasIndex(sc => sc.ConfigurationKey)
                .IsUnique()
                .HasDatabaseName("UX_SystemConfigurations_Key");

            // ParameterChanges 索引
            modelBuilder.Entity<ParameterChange>()
                .HasIndex(pc => new { pc.ExperimentId, pc.Timestamp })
                .HasDatabaseName("IX_ParameterChanges_ExperimentId_Timestamp");

            // SystemLogs 索引
            modelBuilder.Entity<SystemLog>()
                .HasIndex(sl => new { sl.Level, sl.Timestamp })
                .HasDatabaseName("IX_SystemLogs_Level_Timestamp");

            // SecurityEvents 索引
            modelBuilder.Entity<SecurityEvent>()
                .HasIndex(se => new { se.EventType, se.Severity, se.Timestamp })
                .HasDatabaseName("IX_SecurityEvents_Type_Severity");

            // AlarmRecords 索引
            modelBuilder.Entity<AlarmRecord>()
                .HasIndex(ar => new { ar.Level, ar.CreateTime })
                .HasDatabaseName("IX_AlarmRecords_Level_CreateTime");

            modelBuilder.Entity<AlarmRecord>()
                .HasIndex(ar => new { ar.IsConfirmed, ar.CreateTime })
                .HasDatabaseName("IX_AlarmRecords_IsConfirmed_CreateTime");

            modelBuilder.Entity<AlarmRecord>()
                .HasIndex(ar => ar.Type)
                .HasDatabaseName("IX_AlarmRecords_Type");
        }

        private void ConfigureRelationships(ModelBuilder modelBuilder)
        {
            // 配置级联删除
            modelBuilder.Entity<DataPoint>()
                .HasOne(dp => dp.Experiment)
                .WithMany(e => e.DataPoints)
                .HasForeignKey(dp => dp.ExperimentId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<ParameterChange>()
                .HasOne(pc => pc.Experiment)
                .WithMany(e => e.ParameterChanges)
                .HasForeignKey(pc => pc.ExperimentId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<ExperimentState>()
                .HasOne(es => es.Experiment)
                .WithMany()
                .HasForeignKey(es => es.ExperimentId)
                .OnDelete(DeleteBehavior.Cascade);

            // 添加 DeviceStatus 的外键关系配置
            modelBuilder.Entity<PEMTestSystem.Models.Devices.DeviceStatus>()
                .HasOne(ds => ds.Device)
                .WithMany()
                .HasForeignKey(ds => ds.DeviceId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<PEMTestSystem.Models.Devices.DeviceStatus>()
                .HasOne(ds => ds.Experiment)
                .WithMany()
                .HasForeignKey(ds => ds.ExperimentId)
                .OnDelete(DeleteBehavior.SetNull);
        }

        private void ConfigureConstraints(ModelBuilder modelBuilder)
        {
            // 显式配置主键
            modelBuilder.Entity<PEMTestSystem.Models.Devices.DeviceStatus>()
                .HasKey(ds => ds.Id);

            // 配置默认值 - SQLite兼容版本
            modelBuilder.Entity<Experiment>()
                .Property(e => e.CreatedAt)
                .HasDefaultValueSql("datetime('now')");

            modelBuilder.Entity<Experiment>()
                .Property(e => e.UpdatedAt)
                .HasDefaultValueSql("datetime('now')");

        }

        private void ConfigureSeedData(ModelBuilder modelBuilder)
        {
            // 种子数据将在 DatabaseInitializer 中处理
        }

        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            // 自动更新审计字段
            var entries = ChangeTracker.Entries()
                .Where(e => e.State == EntityState.Added || e.State == EntityState.Modified);

            foreach (var entry in entries)
            {
                if (entry.Entity is Experiment experiment)
                {
                    if (entry.State == EntityState.Added)
                    {
                        experiment.CreatedAt = DateTime.Now;
                    }
                    experiment.UpdatedAt = DateTime.Now;
                }

                if (entry.Entity is ExperimentTemplate template)
                {
                    if (entry.State == EntityState.Added)
                    {
                        template.CreatedAt = DateTime.Now;
                    }
                    template.UpdatedAt = DateTime.Now;
                }
            }

            return await base.SaveChangesAsync(cancellationToken);
        }
    }
}

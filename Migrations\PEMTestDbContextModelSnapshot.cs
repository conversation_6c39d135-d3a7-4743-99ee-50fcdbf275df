﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using PEMTestSystem.Data;

#nullable disable

namespace PEMTestSystem.Migrations
{
    [DbContext(typeof(PEMTestDbContext))]
    partial class PEMTestDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.0")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("PEMTestSystem.Models.AlarmRecord", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime?>("ConfirmTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("ConfirmUser")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsConfirmed")
                        .HasColumnType("bit");

                    b.Property<int>("Level")
                        .HasColumnType("int");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("Type")
                        .HasDatabaseName("IX_AlarmRecords_Type");

                    b.HasIndex("IsConfirmed", "CreateTime")
                        .HasDatabaseName("IX_AlarmRecords_IsConfirmed_CreateTime");

                    b.HasIndex("Level", "CreateTime")
                        .HasDatabaseName("IX_AlarmRecords_Level_CreateTime");

                    SqlServerIndexBuilderExtensions.IncludeProperties(b.HasIndex("Level", "CreateTime"), new[] { "Type", "IsConfirmed" });

                    b.ToTable("AlarmRecords");
                });

            modelBuilder.Entity("PEMTestSystem.Models.Control.ExperimentState", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("CanRecover")
                        .HasColumnType("bit");

                    b.Property<string>("Configuration")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("CurrentCycle")
                        .HasColumnType("int");

                    b.Property<decimal>("CurrentProgress")
                        .HasColumnType("decimal(5,2)");

                    b.Property<int>("CurrentStatus")
                        .HasColumnType("int");

                    b.Property<DateTime?>("DeviceReadyTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeviceStates")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("ExperimentId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("ExperimentStartTime")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("LastSaveTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("RecoveryNotes")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<DateTime?>("StartTime")
                        .HasColumnType("datetime2");

                    b.Property<int>("TotalCycles")
                        .HasColumnType("int");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("ExperimentId");

                    b.ToTable("ExperimentStates");
                });

            modelBuilder.Entity("PEMTestSystem.Models.Control.ParameterChange", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("ChangeDescription")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("ChangeReason")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int>("ChangeSource")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<decimal>("ElapsedSeconds")
                        .HasColumnType("decimal(18,3)");

                    b.Property<Guid>("ExperimentId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal>("NewValue")
                        .HasColumnType("decimal(18,6)");

                    b.Property<decimal>("OldValue")
                        .HasColumnType("decimal(18,6)");

                    b.Property<string>("ParameterCategory")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("ParameterName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime>("Timestamp")
                        .HasColumnType("datetime2");

                    b.Property<string>("Unit")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.HasKey("Id");

                    b.HasIndex("ExperimentId", "Timestamp")
                        .HasDatabaseName("IX_ParameterChanges_ExperimentId_Timestamp");

                    b.ToTable("ParameterChanges");
                });

            modelBuilder.Entity("PEMTestSystem.Models.Core.DataPoint", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<decimal>("Current")
                        .HasColumnType("decimal(10,2)");

                    b.Property<decimal>("ElapsedSeconds")
                        .HasColumnType("decimal(18,3)");

                    b.Property<Guid>("ExperimentId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal>("FlowRate1")
                        .HasColumnType("decimal(8,1)");

                    b.Property<decimal>("FlowRate2")
                        .HasColumnType("decimal(8,1)");

                    b.Property<string>("Notes")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int>("Quality")
                        .HasColumnType("int");

                    b.Property<decimal>("Temperature")
                        .HasColumnType("decimal(5,1)");

                    b.Property<DateTime>("Timestamp")
                        .HasColumnType("datetime2(3)");

                    b.Property<decimal>("Voltage")
                        .HasColumnType("decimal(10,3)");

                    b.HasKey("Id");

                    b.HasIndex("ExperimentId", "ElapsedSeconds")
                        .HasDatabaseName("IX_DataPoints_ExperimentId_ElapsedSeconds");

                    b.HasIndex("ExperimentId", "Timestamp")
                        .HasDatabaseName("IX_DataPoints_ExperimentId_Timestamp_Covering");

                    SqlServerIndexBuilderExtensions.IncludeProperties(b.HasIndex("ExperimentId", "Timestamp"), new[] { "Voltage", "Current", "Temperature", "FlowRate1", "FlowRate2", "ElapsedSeconds" });

                    b.HasIndex("Quality", "Timestamp")
                        .HasDatabaseName("IX_DataPoints_Quality_Timestamp");

                    b.ToTable("DataPoints");
                });

            modelBuilder.Entity("PEMTestSystem.Models.Core.Experiment", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int?>("ActualDuration")
                        .HasColumnType("int");

                    b.Property<string>("Configuration")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETDATE()");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<DateTime?>("DeviceReadyTime")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("ExperimentEndTime")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("ExperimentStartTime")
                        .HasColumnType("datetime2");

                    b.Property<int>("ExperimentType")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int?>("PlannedDuration")
                        .HasColumnType("int");

                    b.Property<int?>("PreheatingDuration")
                        .HasColumnType("int");

                    b.Property<DateTime?>("RequestTime")
                        .HasColumnType("datetime2");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<long>("TotalDataPoints")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("UpdatedAt")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETDATE()");

                    b.HasKey("Id");

                    b.HasIndex("ExperimentStartTime")
                        .HasDatabaseName("IX_Experiments_StartTime");

                    b.HasIndex("ExperimentType", "Status")
                        .HasDatabaseName("IX_Experiments_Type_Status");

                    b.ToTable("Experiments");
                });

            modelBuilder.Entity("PEMTestSystem.Models.Core.ExperimentTemplate", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Configuration")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<int>("ExperimentType")
                        .HasColumnType("int");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("bit");

                    b.Property<bool>("IsSystem")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .HasDatabaseName("IX_ExperimentTemplates_Name");

                    b.HasIndex("ExperimentType", "IsDefault")
                        .HasDatabaseName("IX_ExperimentTemplates_Type_Default");

                    b.ToTable("ExperimentTemplates");
                });

            modelBuilder.Entity("PEMTestSystem.Models.Devices.Device", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ConnectionString")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int>("ConnectionType")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DeviceId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("DeviceName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("DeviceType")
                        .HasColumnType("int");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("LastCalibrationDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Model")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Specifications")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("DeviceId")
                        .IsUnique()
                        .HasDatabaseName("UX_Devices_DeviceId");

                    b.HasIndex("DeviceType")
                        .HasDatabaseName("IX_Devices_Type");

                    b.ToTable("Devices");
                });

            modelBuilder.Entity("PEMTestSystem.Models.Devices.DeviceStatus", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("CurrentParameters")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("DeviceId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("DeviceId1")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ExperimentId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ExperimentId1")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("LastSuccessfulCommunication")
                        .HasColumnType("datetime2");

                    b.Property<int?>("ResponseTime")
                        .HasColumnType("int");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("StatusDetails")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("Timestamp")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("DeviceId1");

                    b.HasIndex("ExperimentId");

                    b.HasIndex("ExperimentId1");

                    b.HasIndex("DeviceId", "Timestamp")
                        .HasDatabaseName("IX_DeviceStatus_DeviceId_Timestamp");

                    b.ToTable("DeviceStatuses");
                });

            modelBuilder.Entity("PEMTestSystem.Models.Export.DataExport", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("CompletedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("EndTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("ErrorMessage")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("ExperimentId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ExportFormat")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("ExportName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("FilePath")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<long?>("FileSize")
                        .HasColumnType("bigint");

                    b.Property<bool>("IncludeDeviceStatus")
                        .HasColumnType("bit");

                    b.Property<bool>("IncludeParameterChanges")
                        .HasColumnType("bit");

                    b.Property<long?>("RecordCount")
                        .HasColumnType("bigint");

                    b.Property<string>("SelectedColumns")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<DateTime?>("StartTime")
                        .HasColumnType("datetime2");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("TimeFormat")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.HasKey("Id");

                    b.HasIndex("ExperimentId");

                    b.ToTable("DataExports");
                });

            modelBuilder.Entity("PEMTestSystem.Models.Security.SecurityEvent", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("ActionTaken")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<Guid?>("DeviceId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("EventDescription")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EventTitle")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int>("EventType")
                        .HasColumnType("int");

                    b.Property<Guid?>("ExperimentId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsResolved")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("ResolvedAt")
                        .HasColumnType("datetime2");

                    b.Property<int>("Severity")
                        .HasColumnType("int");

                    b.Property<string>("SystemStateSnapshot")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("Timestamp")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("DeviceId");

                    b.HasIndex("ExperimentId");

                    b.HasIndex("EventType", "Severity", "Timestamp")
                        .HasDatabaseName("IX_SecurityEvents_Type_Severity");

                    b.ToTable("SecurityEvents");
                });

            modelBuilder.Entity("PEMTestSystem.Models.System.SystemConfiguration", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Category")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("ConfigurationKey")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("ConfigurationValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("DataType")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<bool>("IsEncrypted")
                        .HasColumnType("bit");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("ConfigurationKey")
                        .IsUnique()
                        .HasDatabaseName("UX_SystemConfigurations_Key");

                    b.ToTable("SystemConfigurations");
                });

            modelBuilder.Entity("PEMTestSystem.Models.System.SystemLog", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"));

                    b.Property<string>("Category")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid?>("DeviceId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Exception")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("ExperimentId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Level")
                        .HasColumnType("int");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Properties")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Source")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<DateTime>("Timestamp")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("DeviceId");

                    b.HasIndex("ExperimentId");

                    b.HasIndex("Level", "Timestamp")
                        .HasDatabaseName("IX_SystemLogs_Level_Timestamp");

                    b.ToTable("SystemLogs");
                });

            modelBuilder.Entity("PEMTestSystem.Models.Control.ExperimentState", b =>
                {
                    b.HasOne("PEMTestSystem.Models.Core.Experiment", "Experiment")
                        .WithMany()
                        .HasForeignKey("ExperimentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Experiment");
                });

            modelBuilder.Entity("PEMTestSystem.Models.Control.ParameterChange", b =>
                {
                    b.HasOne("PEMTestSystem.Models.Core.Experiment", "Experiment")
                        .WithMany("ParameterChanges")
                        .HasForeignKey("ExperimentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Experiment");
                });

            modelBuilder.Entity("PEMTestSystem.Models.Core.DataPoint", b =>
                {
                    b.HasOne("PEMTestSystem.Models.Core.Experiment", "Experiment")
                        .WithMany("DataPoints")
                        .HasForeignKey("ExperimentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Experiment");
                });

            modelBuilder.Entity("PEMTestSystem.Models.Devices.DeviceStatus", b =>
                {
                    b.HasOne("PEMTestSystem.Models.Devices.Device", "Device")
                        .WithMany()
                        .HasForeignKey("DeviceId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("PEMTestSystem.Models.Devices.Device", null)
                        .WithMany("DeviceStatuses")
                        .HasForeignKey("DeviceId1");

                    b.HasOne("PEMTestSystem.Models.Core.Experiment", "Experiment")
                        .WithMany()
                        .HasForeignKey("ExperimentId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("PEMTestSystem.Models.Core.Experiment", null)
                        .WithMany("DeviceStatuses")
                        .HasForeignKey("ExperimentId1");

                    b.Navigation("Device");

                    b.Navigation("Experiment");
                });

            modelBuilder.Entity("PEMTestSystem.Models.Export.DataExport", b =>
                {
                    b.HasOne("PEMTestSystem.Models.Core.Experiment", "Experiment")
                        .WithMany()
                        .HasForeignKey("ExperimentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Experiment");
                });

            modelBuilder.Entity("PEMTestSystem.Models.Security.SecurityEvent", b =>
                {
                    b.HasOne("PEMTestSystem.Models.Devices.Device", "Device")
                        .WithMany()
                        .HasForeignKey("DeviceId");

                    b.HasOne("PEMTestSystem.Models.Core.Experiment", "Experiment")
                        .WithMany("SecurityEvents")
                        .HasForeignKey("ExperimentId");

                    b.Navigation("Device");

                    b.Navigation("Experiment");
                });

            modelBuilder.Entity("PEMTestSystem.Models.System.SystemLog", b =>
                {
                    b.HasOne("PEMTestSystem.Models.Devices.Device", "Device")
                        .WithMany()
                        .HasForeignKey("DeviceId");

                    b.HasOne("PEMTestSystem.Models.Core.Experiment", "Experiment")
                        .WithMany("SystemLogs")
                        .HasForeignKey("ExperimentId");

                    b.Navigation("Device");

                    b.Navigation("Experiment");
                });

            modelBuilder.Entity("PEMTestSystem.Models.Core.Experiment", b =>
                {
                    b.Navigation("DataPoints");

                    b.Navigation("DeviceStatuses");

                    b.Navigation("ParameterChanges");

                    b.Navigation("SecurityEvents");

                    b.Navigation("SystemLogs");
                });

            modelBuilder.Entity("PEMTestSystem.Models.Devices.Device", b =>
                {
                    b.Navigation("DeviceStatuses");
                });
#pragma warning restore 612, 618
        }
    }
}

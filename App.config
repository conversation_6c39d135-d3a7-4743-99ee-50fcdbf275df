<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <!-- 连接字符串配置 -->
  <connectionStrings>
    <add name="DefaultConnection"
         connectionString="Data Source=PEMTestSystem.db"
         providerName="Microsoft.Data.Sqlite" />
  </connectionStrings>

  <!-- 应用程序设置 -->
  <appSettings>
    <!-- 数据库初始化设置 -->
    <add key="AutoCreateDatabase" value="true" />
    <add key="AutoMigrate" value="true" />
    <add key="SeedDefaultData" value="true" />
    
    <!-- 安全设置 -->
    <add key="MaxVoltage" value="10.0" />
    <add key="MaxCurrent" value="170.0" />
    <add key="MaxTemperature" value="95.0" />
    <add key="EmergencyStopTimeout" value="500" />
    <add key="DeviceCommunicationTimeout" value="5" />
    
    <!-- 数据采集设置 -->
    <add key="DefaultSamplingInterval" value="1.0" />
    <add key="MaxDataPointsInMemory" value="10000" />
    <add key="DataRetentionDays" value="365" />
    
    <!-- 日志设置 -->
    <add key="LogLevel" value="Information" />
    <add key="EnableConsoleLogging" value="true" />
    <add key="EnableFileLogging" value="true" />
    <add key="LogFilePath" value="Logs\PEMTestSystem.log" />
  </appSettings>

  <!-- 系统配置 -->
  <system.diagnostics>
    <trace autoflush="true">
      <listeners>
        <add name="textWriterTraceListener" 
             type="System.Diagnostics.TextWriterTraceListener" 
             initializeData="Logs\trace.log" />
      </listeners>
    </trace>
  </system.diagnostics>

  <!-- 运行时配置 -->
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.DependencyInjection" 
                          publicKeyToken="adb9793829ddae60" 
                          culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-8.0.0.0" newVersion="8.0.0.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>
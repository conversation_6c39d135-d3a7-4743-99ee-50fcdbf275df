using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Globalization;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Media;
using LiveCharts;
using LiveCharts.Configurations;
using LiveCharts.Wpf;
using LiveCharts.Defaults;
using Microsoft.Extensions.DependencyInjection;
using PEMTestSystem.Models.Core;
using PEMTestSystem.Services;
using PEMTestSystem.Services.Devices;
// 明确指定Separator类型，避免冲突
using WpfSeparator = System.Windows.Controls.Separator;
using LiveChartsSeparator = LiveCharts.Wpf.Separator;

namespace PEMTestSystem
{
    // A simple data class to hold device status.
    // INotifyPropertyChanged is crucial for UI to update automatically when properties change.
    public class DeviceStatus : INotifyPropertyChanged
    {
        private string _name;
        public string Name
        {
            get => _name;
            set { _name = value; OnPropertyChanged(nameof(Name)); }
        }

        private bool _isConnected;
        public bool IsConnected
        {
            get => _isConnected;
            set { _isConnected = value; OnPropertyChanged(nameof(IsConnected)); }
        }

        private bool _isEnabled = true;
        public bool IsEnabled
        {
            get => _isEnabled;
            set { _isEnabled = value; OnPropertyChanged(nameof(IsEnabled)); }
        }

        private bool _canBeDisabled = true;
        public bool CanBeDisabled
        {
            get => _canBeDisabled;
            set { _canBeDisabled = value; OnPropertyChanged(nameof(CanBeDisabled)); }
        }

        public event PropertyChangedEventHandler? PropertyChanged;
        protected void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public partial class MainWindow : Window, INotifyPropertyChanged
    {
        public List<DeviceStatus> Devices { get; set; }

        // LiveCharts 相关属性 - 修复为正确的集合类型
        private SeriesCollection _chartSeries;
        public SeriesCollection ChartSeries
        {
            get => _chartSeries;
            set { _chartSeries = value; OnPropertyChanged(nameof(ChartSeries)); }
        }

        private AxesCollection _xAxis;
        public AxesCollection XAxis
        {
            get => _xAxis;
            set { _xAxis = value; OnPropertyChanged(nameof(XAxis)); }
        }

        private AxesCollection _yAxis;
        public AxesCollection YAxis
        {
            get => _yAxis;
            set { _yAxis = value; OnPropertyChanged(nameof(YAxis)); }
        }

        // 数据存储
        private ChartValues<ObservablePoint> _voltageTimeData;
        private ChartValues<ObservablePoint> _currentTimeData;
        private ChartValues<ObservablePoint> _currentVoltageData;

        private System.Windows.Threading.DispatcherTimer _dataTimer;
        private DateTime _experimentStartTime;
        private ExperimentMode _currentMode = ExperimentMode.ConstantCurrent;

        // Services
        private readonly DataAcquisitionService _dataAcquisitionService;
        private readonly DeviceConfigurationService _configService;
        private readonly DeviceManager _deviceManager;

        // Current data point
        private DataPoint? _currentDataPoint;

        public enum ExperimentMode
        {
            ConstantCurrent,
            ConstantVoltage, 
            LinearScan
        }

        public MainWindow()
        {
            InitializeComponent();

            // Get services from DI container
            var app = (App)Application.Current;
            _dataAcquisitionService = app.Host?.Services.GetRequiredService<DataAcquisitionService>()
                ?? throw new InvalidOperationException("无法获取数据采集服务");
            _configService = app.Host?.Services.GetRequiredService<DeviceConfigurationService>()
                ?? throw new InvalidOperationException("无法获取设备配置服务");
            _deviceManager = app.Host?.Services.GetRequiredService<DeviceManager>()
                ?? throw new InvalidOperationException("无法获取设备管理器");

            InitializeServices();
            SetupEventHandlers();
            LoadDeviceStatus();
            InitializeCharts();
            InitializeDataAcquisition();
            InitializeExperimentParameters();

            // 订阅设备管理器事件
            SubscribeToDeviceManagerEvents();

            // Set the DataContext for data binding
            this.DataContext = this;
        }

        private void InitializeServices()
        {
            try
            {
                App.AlarmService.Info("主窗口", "初始化服务");
                // 这里可以添加其他服务的初始化逻辑
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("主窗口", "服务初始化失败", ex);
            }
        }

        private void SetupEventHandlers()
        {
            try
            {
                // 设置实验参数文本框的事件处理器
                SetupParameterTextBoxEvents();
                App.AlarmService.Info("主窗口", "事件处理器设置完成");
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("主窗口", "事件处理器设置失败", ex);
            }
        }

        private void SetupParameterTextBoxEvents()
        {
            // 由于我们已经在 XAML 中为所有参数 TextBox 添加了 TextChanged="ParameterTextBox_TextChanged"
            // 这里不需要额外的设置，事件处理器会自动绑定
            App.AlarmService.Debug("主窗口", "参数文本框事件处理器已通过 XAML 绑定");
        }

        private void InitializeExperimentParameters()
        {
            try
            {
                // 初始化实验参数默认值
                // 由于 XAML 中已经设置了默认值，这里主要是确保状态正确
                App.AlarmService.Info("主窗口", "实验参数初始化完成");
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("主窗口", "实验参数初始化失败", ex);
            }
        }

        private void SubscribeToDeviceManagerEvents()
        {
            try
            {
                // 订阅设备启用状态变化事件
                _deviceManager.DeviceEnabledStatusChanged += OnDeviceEnabledStatusChanged;
                _deviceManager.DeviceStatusChanged += OnDeviceStatusChanged;

                App.AlarmService.Info("主窗口", "设备管理器事件订阅完成");
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("主窗口", "订阅设备管理器事件失败", ex);
            }
        }

        private void OnDeviceEnabledStatusChanged(object? sender, DeviceEnabledStatusChangedEventArgs e)
        {
            try
            {
                Dispatcher.Invoke(() =>
                {
                    // 查找对应的设备并更新UI状态
                    var device = Devices.FirstOrDefault(d => GetDeviceIdByName(d.Name) == e.DeviceId);
                    if (device != null)
                    {
                        device.IsEnabled = e.IsEnabled;
                        if (!e.IsEnabled)
                        {
                            device.IsConnected = false; // 禁用时断开连接
                        }

                        App.AlarmService.Info("UI更新", $"设备 {device.Name} 启用状态已更新: {(e.IsEnabled ? "启用" : "禁用")}");
                    }
                });
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("主窗口", "处理设备启用状态变化事件失败", ex);
            }
        }

        private void OnDeviceStatusChanged(object? sender, DeviceStatusChangedEventArgs e)
        {
            try
            {
                Dispatcher.Invoke(() =>
                {
                    // 查找对应的设备并更新连接状态
                    var device = Devices.FirstOrDefault(d => GetDeviceIdByName(d.Name) == e.DeviceId);
                    if (device != null && device.IsEnabled) // 只有启用的设备才更新连接状态
                    {
                        device.IsConnected = e.NewStatus == Services.Devices.DeviceStatus.Connected ||
                                           e.NewStatus == Services.Devices.DeviceStatus.Running;

                        App.AlarmService.Debug("UI更新", $"设备 {device.Name} 连接状态已更新: {(device.IsConnected ? "已连接" : "未连接")}");
                    }
                });
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("主窗口", "处理设备状态变化事件失败", ex);
            }
        }

        private void InitializeDataAcquisition()
        {
            try
            {
                // 订阅数据采集事件
                _dataAcquisitionService.DataPointReceived += OnDataPointReceived;
                _dataAcquisitionService.StatusChanged += OnAcquisitionStatusChanged;

                App.AlarmService.Info("主窗口", "数据采集服务初始化完成");
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("主窗口", "数据采集服务初始化失败", ex);
            }
        }

        private void OnDataPointReceived(object? sender, DataPointEventArgs e)
        {
            try
            {
                // 确保在UI线程中更新
                Dispatcher.Invoke(() =>
                {
                    _currentDataPoint = e.DataPoint;
                    UpdateRealTimeDisplay(e.DataPoint);
                    UpdateChartData(e.DataPoint);
                });
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("主窗口", "更新实时数据显示失败", ex);
            }
        }

        private void OnAcquisitionStatusChanged(object? sender, AcquisitionStatusEventArgs e)
        {
            try
            {
                Dispatcher.Invoke(() =>
                {
                    // 更新状态显示
                    App.AlarmService.Info("数据采集", $"状态变更: {e.Message}");
                });
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("主窗口", "更新采集状态失败", ex);
            }
        }

        private void UpdateRealTimeDisplay(DataPoint dataPoint)
        {
            try
            {
                // 更新实时数据显示
                VoltageValueText.Text = $"{dataPoint.Voltage:F3} V";
                CurrentValueText.Text = $"{dataPoint.Current:F2} A";
                TemperatureValueText.Text = $"{dataPoint.Temperature:F1} °C";
                FlowRate1ValueText.Text = $"{dataPoint.FlowRate1:F2} mL/min";
                FlowRate2ValueText.Text = $"{dataPoint.FlowRate2:F2} mL/min";

                // 更新设备状态指示器
                UpdateDeviceStatusIndicators();
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("主窗口", "更新实时显示失败", ex);
            }
        }





        private void UpdateDeviceStatusIndicators()
        {
            try
            {
                // 这里应该从设备管理器获取实际的连接状态
                // 暂时使用模拟状态
                var successBrush = FindResource("SuccessColor") as SolidColorBrush;
                var dangerBrush = FindResource("DangerColor") as SolidColorBrush;

            }
            catch (Exception ex)
            {
                App.AlarmService.Error("主窗口", "更新设备状态指示器失败", ex);
            }
        }

        private void UpdateChartData(DataPoint dataPoint)
        {
            try
            {
                var elapsedSeconds = (double)dataPoint.ElapsedSeconds;

                // 根据当前模式更新图表数据
                switch (_currentMode)
                {
                    case ExperimentMode.ConstantCurrent:
                        // 恒流模式：显示电压-时间
                        _voltageTimeData.Add(new ObservablePoint(elapsedSeconds, (double)dataPoint.Voltage));
                        break;

                    case ExperimentMode.ConstantVoltage:
                        // 恒压模式：显示电流-时间
                        _currentTimeData.Add(new ObservablePoint(elapsedSeconds, (double)dataPoint.Current));
                        break;

                    case ExperimentMode.LinearScan:
                        // 线扫模式：显示电流-电压
                        _currentVoltageData.Add(new ObservablePoint((double)dataPoint.Voltage, (double)dataPoint.Current));
                        break;
                }

                // 限制数据点数量以避免内存问题
                const int maxDataPoints = 1000;
                if (_voltageTimeData.Count > maxDataPoints)
                {
                    _voltageTimeData.RemoveAt(0);
                }
                if (_currentTimeData.Count > maxDataPoints)
                {
                    _currentTimeData.RemoveAt(0);
                }
                if (_currentVoltageData.Count > maxDataPoints)
                {
                    _currentVoltageData.RemoveAt(0);
                }
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("主窗口", "更新图表数据失败", ex);
            }
        }

        private void InitializeCharts()
        {
            // 初始化数据集合
            _voltageTimeData = new ChartValues<ObservablePoint>();
            _currentTimeData = new ChartValues<ObservablePoint>();
            _currentVoltageData = new ChartValues<ObservablePoint>();

            // 添加默认数据以便显示
            GenerateDefaultData();

            // 初始化为恒流模式图表
            SetupConstantCurrentChart();

            // 设置数据更新定时器
            _dataTimer = new System.Windows.Threading.DispatcherTimer();
            _dataTimer.Interval = TimeSpan.FromSeconds(1);
            _dataTimer.Tick += DataTimer_Tick;
        }

        private void GenerateDefaultData()
        {
            // 生成恒流模式默认数据 (电压-时间)
            for (int i = 0; i <= 50; i++)
            {
                var time = i * 10.0; // 时间点
                var voltage = 1.8 + 0.05 * Math.Sin(time * 0.05) + (new Random(i).NextDouble() - 0.5) * 0.02;
                _voltageTimeData.Add(new ObservablePoint(time, voltage));
            }

            // 生成恒压模式默认数据 (电流-时间)
            for (int i = 0; i <= 50; i++)
            {
                var time = i * 10.0;
                var current = 20 + 1.5 * Math.Sin(time * 0.03) + (new Random(i + 100).NextDouble() - 0.5) * 0.8;
                _currentTimeData.Add(new ObservablePoint(time, current));
            }

            // 生成线扫模式默认数据 (电流-电压)
            for (int i = 0; i <= 50; i++)
            {
                var voltage = 1.2 + i * 0.02; // 电压从1.2V到2.2V
                var current = Math.Max(0, (voltage - 1.2) * 25 + (new Random(i + 200).NextDouble() - 0.5) * 2);
                _currentVoltageData.Add(new ObservablePoint(voltage, current));
            }
        }

        private void SetupConstantCurrentChart()
        {
            ChartSeries = new SeriesCollection
            {
                new LineSeries
                {
                    Title = "电压",
                    Values = _voltageTimeData,
                    PointGeometry = null,
                    LineSmoothness = 0.3,
                    Stroke = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#00aaff")),
                    Fill = Brushes.Transparent,
                    StrokeThickness = 2
                }
            };

            XAxis = new AxesCollection
            {
                new Axis
                {
                    Title = "时间 (s)",
                    Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#c0c2c9")),
                    FontSize = 12,
                    Separator = new LiveChartsSeparator 
                    { 
                        StrokeThickness = 1,
                        Stroke = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#3a3f51"))
                    }
                }
            };

            YAxis = new AxesCollection
            {
                new Axis
                {
                    Title = "电压 (V)",
                    Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#c0c2c9")),
                    FontSize = 12,
                    LabelFormatter = value => value.ToString("F2"),
                    Separator = new LiveChartsSeparator 
                    { 
                        StrokeThickness = 1,
                        Stroke = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#3a3f51"))
                    }
                }
            };

            if (FindName("ChartTitleText") is TextBlock titleText)
            {
                titleText.Text = "恒流模式: 电压-时间 (V-t) 曲线";
            }
        }

        private void SetupConstantVoltageChart()
        {
            ChartSeries = new SeriesCollection
            {
                new LineSeries
                {
                    Title = "电流",
                    Values = _currentTimeData,
                    PointGeometry = null,
                    LineSmoothness = 0.3,
                    Stroke = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#00ff9b")),
                    Fill = Brushes.Transparent,
                    StrokeThickness = 2
                }
            };

            XAxis = new AxesCollection
            {
                new Axis
                {
                    Title = "时间 (s)",
                    Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#c0c2c9")),
                    FontSize = 12,
                    Separator = new LiveChartsSeparator 
                    { 
                        StrokeThickness = 1,
                        Stroke = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#3a3f51"))
                    }
                }
            };

            YAxis = new AxesCollection
            {
                new Axis
                {
                    Title = "电流 (A)",
                    Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#c0c2c9")),
                    FontSize = 12,
                    LabelFormatter = value => value.ToString("F2"),
                    Separator = new LiveChartsSeparator 
                    { 
                        StrokeThickness = 1,
                        Stroke = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#3a3f51"))
                    }
                }
            };

            if (FindName("ChartTitleText") is TextBlock titleText)
            {
                titleText.Text = "恒压模式: 电流-时间 (I-t) 曲线";
            }
        }

        private void SetupLinearScanChart()
        {
            ChartSeries = new SeriesCollection
            {
                new LineSeries
                {
                    Title = "I-V曲线",
                    Values = _currentVoltageData,
                    PointGeometry = null,
                    LineSmoothness = 0.3,
                    Stroke = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#ff4d4d")),
                    Fill = Brushes.Transparent,
                    StrokeThickness = 2
                }
            };

            XAxis = new AxesCollection
            {
                new Axis
                {
                    Title = "电压 (V)",
                    Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#c0c2c9")),
                    FontSize = 12,
                    Separator = new LiveChartsSeparator 
                    { 
                        StrokeThickness = 1,
                        Stroke = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#3a3f51"))
                    }
                }
            };

            YAxis = new AxesCollection
            {
                new Axis
                {
                    Title = "电流 (A)",
                    Foreground = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#c0c2c9")),
                    FontSize = 12,
                    LabelFormatter = value => value.ToString("F2"),
                    Separator = new LiveChartsSeparator 
                    { 
                        StrokeThickness = 1,
                        Stroke = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#3a3f51"))
                    }
                }
            };

            if (FindName("ChartTitleText") is TextBlock titleText)
            {
                titleText.Text = "线性扫描: 电流-电压 (I-V) 曲线";
            }
        }

        private void ExperimentModeTabControl_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (sender is TabControl tabControl && 
                tabControl.SelectedItem is TabItem selectedTab && 
                selectedTab.Tag != null)
            {
                switch (selectedTab.Tag.ToString())
                {
                    case "ConstantCurrent":
                        _currentMode = ExperimentMode.ConstantCurrent;
                        SetupConstantCurrentChart();
                        break;
                    case "ConstantVoltage":
                        _currentMode = ExperimentMode.ConstantVoltage;
                        SetupConstantVoltageChart();
                        break;
                    case "LinearScan":
                        _currentMode = ExperimentMode.LinearScan;
                        SetupLinearScanChart();
                        break;
                }
            }
        }

        private void DataTimer_Tick(object sender, EventArgs e)
        {
            if (FindName("StartButton") is Button startBtn && !startBtn.IsEnabled) // 实验进行中
            {
                var elapsedTime = (DateTime.Now - _experimentStartTime).TotalSeconds;
                var random = new Random();
                
                switch (_currentMode)
                {
                    case ExperimentMode.ConstantCurrent:
                        var voltage = 1.8 + 0.1 * Math.Sin(elapsedTime * 0.1) + (random.NextDouble() - 0.5) * 0.05;
                        _voltageTimeData.Add(new ObservablePoint(elapsedTime, voltage));
                        if (_voltageTimeData.Count > 100) _voltageTimeData.RemoveAt(0);
                        break;
                        
                    case ExperimentMode.ConstantVoltage:
                        var current = 20 + 2 * Math.Sin(elapsedTime * 0.05) + (random.NextDouble() - 0.5) * 1;
                        _currentTimeData.Add(new ObservablePoint(elapsedTime, current));
                        if (_currentTimeData.Count > 100) _currentTimeData.RemoveAt(0);
                        break;
                        
                    case ExperimentMode.LinearScan:
                        var scanVoltage = 1.2 + (elapsedTime * 0.01);
                        var scanCurrent = Math.Max(0, (scanVoltage - 1.2) * 20 + (random.NextDouble() - 0.5) * 2);
                        _currentVoltageData.Add(new ObservablePoint(scanVoltage, scanCurrent));
                        if (_currentVoltageData.Count > 100) _currentVoltageData.RemoveAt(0);
                        break;
                }
            }
        }

        private async void LoadDeviceStatus()
        {
            try
            {
                // 创建基础设备列表
                Devices = new List<DeviceStatus>
                {
                    new DeviceStatus { Name = "直流电源", IsConnected = false, CanBeDisabled = false, IsEnabled = true },
                    new DeviceStatus { Name = "温控仪", IsConnected = false, CanBeDisabled = true, IsEnabled = true },
                    new DeviceStatus { Name = "流量泵 1", IsConnected = false, CanBeDisabled = true, IsEnabled = true },
                    new DeviceStatus { Name = "流量泵 2", IsConnected = false, CanBeDisabled = true, IsEnabled = true },
                    new DeviceStatus { Name = "数据库", IsConnected = false, CanBeDisabled = false, IsEnabled = true }
                };

                // 从数据库加载实际的启用状态
                foreach (var device in Devices)
                {
                    var deviceId = GetDeviceIdByName(device.Name);
                    var isEnabled = await _deviceManager.IsDeviceEnabledAsync(deviceId);
                    device.IsEnabled = isEnabled;

                    // 如果设备已启用，检查连接状态
                    if (isEnabled)
                    {
                        var deviceInstance = _deviceManager.GetDevice<Services.Devices.IDevice>(deviceId);
                        device.IsConnected = deviceInstance?.IsConnected ?? false;
                    }
                }

                App.AlarmService.Info("主窗口", "设备状态加载完成");
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("主窗口", "加载设备状态失败", ex);

                // 如果加载失败，使用默认状态
                Devices = new List<DeviceStatus>
                {
                    new DeviceStatus { Name = "直流电源", IsConnected = false, CanBeDisabled = false, IsEnabled = true },
                    new DeviceStatus { Name = "温控仪", IsConnected = false, CanBeDisabled = true, IsEnabled = true },
                    new DeviceStatus { Name = "流量泵 1", IsConnected = false, CanBeDisabled = true, IsEnabled = true },
                    new DeviceStatus { Name = "流量泵 2", IsConnected = false, CanBeDisabled = true, IsEnabled = true },
                    new DeviceStatus { Name = "数据库", IsConnected = false, CanBeDisabled = false, IsEnabled = true }
                };
            }
        }

        private async void ToggleDeviceState_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is DeviceStatus device)
            {
                try
                {
                    button.IsEnabled = false; // 防止重复点击

                    bool success;
                    if (device.IsEnabled)
                    {
                        // 当前是启用状态，点击后要禁用
                        App.AlarmService.Info("设备控制", $"用户请求禁用设备: {device.Name}");
                        success = await _deviceManager.DisableDeviceAsync(GetDeviceIdByName(device.Name));

                        if (success)
                        {
                            device.IsEnabled = false;
                            device.IsConnected = false;
                            App.AlarmService.Info("设备控制", $"设备 {device.Name} 已成功禁用");
                            MessageBox.Show($"设备 {device.Name} 已禁用", "设备控制", MessageBoxButton.OK, MessageBoxImage.Information);
                        }
                        else
                        {
                            App.AlarmService.Error("设备控制", $"禁用设备 {device.Name} 失败");
                            MessageBox.Show($"禁用设备 {device.Name} 失败，请检查设备状态", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                    else
                    {
                        // 当前是禁用状态，点击后要启用
                        App.AlarmService.Info("设备控制", $"用户请求启用设备: {device.Name}");
                        success = await _deviceManager.EnableDeviceAsync(GetDeviceIdByName(device.Name));

                        if (success)
                        {
                            device.IsEnabled = true;
                            device.IsConnected = true;
                            App.AlarmService.Info("设备控制", $"设备 {device.Name} 已成功启用");
                            MessageBox.Show($"设备 {device.Name} 已启用并连接成功", "设备控制", MessageBoxButton.OK, MessageBoxImage.Information);
                        }
                        else
                        {
                            App.AlarmService.Error("设备控制", $"启用设备 {device.Name} 失败");
                            MessageBox.Show($"启用设备 {device.Name} 失败，请检查设备连接", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                }
                catch (Exception ex)
                {
                    App.AlarmService.Error("设备控制", $"切换设备 {device.Name} 状态异常", ex);
                    MessageBox.Show($"操作失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
                finally
                {
                    button.IsEnabled = true; // 恢复按钮状态
                }
            }
        }

        private async void StartButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (FindName("StartButton") is Button startBtn && FindName("StopButton") is Button stopBtn)
                {
                    startBtn.IsEnabled = false;

                    // 创建新的实验记录
                    var experimentId = Guid.NewGuid();
                    _experimentStartTime = DateTime.Now;

                    // 清空当前数据，开始实时更新
                    switch (_currentMode)
                    {
                        case ExperimentMode.ConstantCurrent:
                            _voltageTimeData.Clear();
                            break;
                        case ExperimentMode.ConstantVoltage:
                            _currentTimeData.Clear();
                            break;
                        case ExperimentMode.LinearScan:
                            _currentVoltageData.Clear();
                            break;
                    }

                    // 启动数据采集
                    var success = await _dataAcquisitionService.StartAcquisitionAsync(experimentId);
                    if (success)
                    {
                        stopBtn.IsEnabled = true;
                        _dataTimer.Start();
                        App.AlarmService.Info("实验控制", "实验已开始");
                        MessageBox.Show("实验开始！", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        startBtn.IsEnabled = true;
                        MessageBox.Show("启动实验失败，请检查设备连接状态！", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("实验控制", "启动实验异常", ex);
                MessageBox.Show($"启动实验失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);

                // 恢复按钮状态
                if (FindName("StartButton") is Button startBtn)
                {
                    startBtn.IsEnabled = true;
                }
            }
        }

        private async void StopButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (FindName("StartButton") is Button startBtn && FindName("StopButton") is Button stopBtn)
                {
                    stopBtn.IsEnabled = false;
                    _dataTimer.Stop();

                    // 停止数据采集
                    var success = await _dataAcquisitionService.StopAcquisitionAsync();
                    if (success)
                    {
                        startBtn.IsEnabled = true;
                        App.AlarmService.Info("实验控制", "实验已停止");
                        MessageBox.Show("实验已停止。", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        stopBtn.IsEnabled = true;
                        MessageBox.Show("停止实验失败！", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("实验控制", "停止实验异常", ex);
                MessageBox.Show($"停止实验失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);

                // 恢复按钮状态
                if (FindName("StartButton") is Button startBtn && FindName("StopButton") is Button stopBtn)
                {
                    startBtn.IsEnabled = true;
                    stopBtn.IsEnabled = false;
                }
            }
        }

        // 修复 ScanMethod_Checked 方法
        private void ScanMethod_Checked(object sender, RoutedEventArgs e)
        {
            var scanRateGroup = FindName("ScanRateGroup") as Grid;
            var scanTimeGroup = FindName("ScanTimeGroup") as Grid;
            var scanByRateRadio = FindName("ScanByRateRadio") as RadioButton;

            if (scanRateGroup == null || scanTimeGroup == null || scanByRateRadio == null) return;

            if (scanByRateRadio.IsChecked == true)
            {
                scanRateGroup.Visibility = Visibility.Visible;
                scanTimeGroup.Visibility = Visibility.Collapsed;
            }
            else
            {
                scanRateGroup.Visibility = Visibility.Collapsed;
                scanTimeGroup.Visibility = Visibility.Visible;
            }
        }

        private void ExportChart_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("导出当前波形数据功能待实现", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ShowHistory_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("历史记录窗口功能待实现", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void DeviceSettingsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                App.AlarmService.Debug("设备设置", "DeviceSettingsButton_Click 事件被触发");

                DeviceSettingsWindow deviceSettingsWindow;

                // 检查是否是来自设备列表中的设置按钮
                if (sender is Button button && button.Tag is string deviceName)
                {
                    // 来自设备列表的设置按钮，打开特定设备的设置
                    App.AlarmService.Info("设备设置", $"打开设备 {deviceName} 的设置窗口");
                    deviceSettingsWindow = new DeviceSettingsWindow(deviceName);
                }
                else
                {
                    // 来自通用设置按钮，打开所有设备设置
                    App.AlarmService.Info("设备设置", "打开通用设备设置窗口");
                    deviceSettingsWindow = new DeviceSettingsWindow();
                }

                deviceSettingsWindow.Owner = this; // 设置父窗口

                // 显示为模态对话框
                bool? result = deviceSettingsWindow.ShowDialog();

                if (result == true)
                {
                    // 用户点击了保存按钮，可以在这里处理保存后的逻辑
                    // 比如重新加载设备状态、刷新连接状态等
                    RefreshDeviceStatus();
                    App.AlarmService.Info("设备设置", "设备设置保存完成，刷新设备状态");
                }
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备设置", "打开设备设置窗口失败", ex);
                MessageBox.Show($"打开设备设置窗口失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void RefreshDeviceStatus()
        {
            // 重新检查设备连接状态
            // TODO: 根据保存的设备设置重新测试连接状态
            
            // 这里可以添加实际的设备状态检查逻辑
            // 现在先使用简单的模拟刷新
            foreach (var device in Devices)
            {
                // 模拟重新检测设备状态
                if (device.IsEnabled)
                {
                    // 这里应该根据实际的设备设置进行连接测试
                    // 暂时使用随机模拟
                    var random = new Random();
                    device.IsConnected = random.NextDouble() > 0.2; // 80%概率连接成功
                }
                else
                {
                    device.IsConnected = false;
                }
            }
            
            // 通知UI更新（由于使用了INotifyPropertyChanged，这一步可能不必要，但保险起见）
            OnPropertyChanged(nameof(Devices));
        }

        protected override async void OnClosed(EventArgs e)
        {
            try
            {
                // 设置应用程序关闭标志
                App.AlarmService?.SetApplicationShuttingDown();

                // 停止数据采集
                if (_dataAcquisitionService != null)
                {
                    await _dataAcquisitionService.StopAcquisitionAsync();
                }

                // 停止定时器
                _dataTimer?.Stop();

                // 取消订阅设备管理器事件
                if (_deviceManager != null)
                {
                    _deviceManager.DeviceEnabledStatusChanged -= OnDeviceEnabledStatusChanged;
                    _deviceManager.DeviceStatusChanged -= OnDeviceStatusChanged;
                }

                App.AlarmService?.Info("主窗口", "主窗口已关闭");
            }
            catch (Exception ex)
            {
                // 在窗口关闭时，使用安全的方式记录异常
                try
                {
                    App.AlarmService?.Error("主窗口", "关闭窗口时发生异常", ex);
                }
                catch
                {
                    // 如果 AlarmService 也失败了，至少记录到控制台
                    System.Console.WriteLine($"主窗口关闭时发生异常: {ex.Message}");
                }
            }
            finally
            {
                base.OnClosed(e);
            }
        }

        /// <summary>
        /// 根据设备名称获取设备ID
        /// </summary>
        private string GetDeviceIdByName(string deviceName)
        {
            return deviceName switch
            {
                "直流电源" => "PowerSupply_Main",
                "温控仪" => "TempController_Main",
                "流量泵 1" => "Pump_01",
                "流量泵 2" => "Pump_02",
                "数据库" => "Database_Main",
                _ => deviceName // 如果没有匹配，直接返回设备名称
            };
        }

        #region INotifyPropertyChanged Implementation
        public event PropertyChangedEventHandler? PropertyChanged;
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
        #endregion

        #region 设备状态面板事件处理

        // 电源设置按钮
        private void PowerSettingsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                App.AlarmService.Info("设备设置", "打开电源设置窗口");

                // 创建模拟的按钮对象，设置 Tag 为设备名称
                var mockButton = new Button { Tag = "直流电源" };

                // 调用统一的设备设置处理方法
                DeviceSettingsButton_Click(mockButton, e);
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备设置", "打开电源设置失败", ex);
                MessageBox.Show($"打开设置失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // 温控器禁用按钮
        private void TemperatureDisableButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                App.AlarmService.Info("设备控制", "温控器禁用/启用操作");
                // TODO: 实现温控器禁用/启用逻辑
                MessageBox.Show("温控器禁用/启用功能待实现", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备控制", "温控器禁用/启用失败", ex);
                MessageBox.Show($"操作失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // 温控器设置按钮
        private void TemperatureSettingsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                App.AlarmService.Info("设备设置", "打开温控器设置窗口");

                // 创建模拟的按钮对象，设置 Tag 为设备名称
                var mockButton = new Button { Tag = "温控仪" };

                // 调用统一的设备设置处理方法
                DeviceSettingsButton_Click(mockButton, e);
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备设置", "打开温控器设置失败", ex);
                MessageBox.Show($"打开设置失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // 流量泵1禁用按钮
        private void FlowPump1DisableButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                App.AlarmService.Info("设备控制", "流量泵1禁用/启用操作");
                // TODO: 实现流量泵1禁用/启用逻辑
                MessageBox.Show("流量泵1禁用/启用功能待实现", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备控制", "流量泵1禁用/启用失败", ex);
                MessageBox.Show($"操作失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // 流量泵1设置按钮
        private void FlowPump1SettingsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                App.AlarmService.Info("设备设置", "打开流量泵1设置窗口");

                // 创建模拟的按钮对象，设置 Tag 为设备名称
                var mockButton = new Button { Tag = "流量泵 1" };

                // 调用统一的设备设置处理方法
                DeviceSettingsButton_Click(mockButton, e);
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备设置", "打开流量泵1设置失败", ex);
                MessageBox.Show($"打开设置失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // 流量泵2禁用按钮
        private void FlowPump2DisableButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                App.AlarmService.Info("设备控制", "流量泵2禁用/启用操作");
                // TODO: 实现流量泵2禁用/启用逻辑
                MessageBox.Show("流量泵2禁用/启用功能待实现", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备控制", "流量泵2禁用/启用失败", ex);
                MessageBox.Show($"操作失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // 流量泵2设置按钮
        private void FlowPump2SettingsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                App.AlarmService.Info("设备设置", "打开流量泵2设置窗口");

                // 创建模拟的按钮对象，设置 Tag 为设备名称
                var mockButton = new Button { Tag = "流量泵 2" };

                // 调用统一的设备设置处理方法
                DeviceSettingsButton_Click(mockButton, e);
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备设置", "打开流量泵2设置失败", ex);
                MessageBox.Show($"打开设置失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // 数据库设置按钮
        private void DatabaseSettingsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                App.AlarmService.Info("设备设置", "打开数据库设置窗口");

                // 创建模拟的按钮对象，设置 Tag 为设备名称
                var mockButton = new Button { Tag = "数据库" };

                // 调用统一的设备设置处理方法
                DeviceSettingsButton_Click(mockButton, e);
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备设置", "打开数据库设置失败", ex);
                MessageBox.Show($"打开设置失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion

        #region 实验参数设置事件处理

        // 配置文件选择按钮
        private void SelectConfigButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var openFileDialog = new Microsoft.Win32.OpenFileDialog
                {
                    Title = "选择实验配置文件",
                    Filter = "JSON 配置文件 (*.json)|*.json|所有文件 (*.*)|*.*",
                    DefaultExt = ".json",
                    InitialDirectory = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Configs")
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    LoadExperimentConfiguration(openFileDialog.FileName);
                    App.AlarmService.Info("配置管理", $"加载配置文件: {openFileDialog.FileName}");
                }
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("配置管理", "选择配置文件失败", ex);
                MessageBox.Show($"选择配置文件失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // 配置文件保存按钮
        private void SaveConfigButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var saveFileDialog = new Microsoft.Win32.SaveFileDialog
                {
                    Title = "保存实验配置文件",
                    Filter = "JSON 配置文件 (*.json)|*.json|所有文件 (*.*)|*.*",
                    DefaultExt = ".json",
                    InitialDirectory = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Configs"),
                    FileName = "新实验配置.json"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    SaveExperimentConfiguration(saveFileDialog.FileName);
                    App.AlarmService.Info("配置管理", $"保存配置文件: {saveFileDialog.FileName}");
                }
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("配置管理", "保存配置文件失败", ex);
                MessageBox.Show($"保存配置文件失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadExperimentConfiguration(string filePath)
        {
            try
            {
                // TODO: 实现配置文件加载逻辑
                // 这里应该读取 JSON 文件并更新界面上的参数值
                MessageBox.Show($"配置文件加载功能待实现\n文件路径: {filePath}", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("配置管理", "加载配置文件失败", ex);
                throw;
            }
        }

        private void SaveExperimentConfiguration(string filePath)
        {
            try
            {
                // TODO: 实现配置文件保存逻辑
                // 这里应该收集界面上的所有参数值并保存为 JSON 文件
                MessageBox.Show($"配置文件保存功能待实现\n文件路径: {filePath}", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("配置管理", "保存配置文件失败", ex);
                throw;
            }
        }

        // 参数验证方法
        private bool ValidateExperimentParameters()
        {
            try
            {
                // TODO: 实现实验参数验证逻辑
                // 验证温度、流量、电流、电压等参数的有效性
                return true;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("参数验证", "验证实验参数失败", ex);
                return false;
            }
        }

        // 参数输入框文本变化事件处理
        private void ParameterTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            try
            {
                if (sender is TextBox textBox)
                {
                    // 实时验证输入的参数值
                    ValidateParameterInput(textBox);
                }
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("参数验证", "参数输入验证失败", ex);
            }
        }

        private void ValidateParameterInput(TextBox textBox)
        {
            try
            {
                string toolTip = textBox.ToolTip?.ToString() ?? "";
                string parameterName = textBox.Name ?? "未知参数";

                bool isValid = false;
                string errorMessage = "";

                if (double.TryParse(textBox.Text, out double value))
                {
                    // 根据参数名称和 ToolTip 进行范围验证
                    isValid = ValidateParameterRange(parameterName, value, toolTip, out errorMessage);
                }
                else
                {
                    errorMessage = "请输入有效的数值";
                }

                // 更新控件样式
                if (isValid)
                {
                    textBox.BorderBrush = FindResource("BorderColor") as SolidColorBrush;
                    textBox.ToolTip = toolTip; // 恢复原始 ToolTip
                }
                else
                {
                    textBox.BorderBrush = FindResource("DangerColor") as SolidColorBrush;
                    textBox.ToolTip = $"{toolTip}\n错误: {errorMessage}";
                }
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("参数验证", "验证参数输入失败", ex);
            }
        }

        private bool ValidateParameterRange(string parameterName, double value, string toolTip, out string errorMessage)
        {
            errorMessage = "";

            try
            {
                // 根据参数名称进行特定的范围验证
                if (parameterName.Contains("Temperature"))
                {
                    if (value < 20 || value > 90)
                    {
                        errorMessage = "温度范围应在 20-90°C 之间";
                        return false;
                    }
                }
                else if (parameterName.Contains("FlowPump"))
                {
                    if (value < 0 || value > 400)
                    {
                        errorMessage = "流量范围应在 0-400 mL/min 之间";
                        return false;
                    }
                }
                else if (parameterName.Contains("Current"))
                {
                    if (value < 0 || value > 170)
                    {
                        errorMessage = "电流范围应在 0-170A 之间";
                        return false;
                    }
                }
                else if (parameterName.Contains("Voltage"))
                {
                    if (value < 0 || value > 10)
                    {
                        errorMessage = "电压范围应在 0-10V 之间";
                        return false;
                    }
                }
                else if (parameterName.Contains("RepeatCount"))
                {
                    if (value < 1 || value > 999 || value != Math.Floor(value))
                    {
                        errorMessage = "重复次数应为 1-999 的整数";
                        return false;
                    }
                }
                else if (parameterName.Contains("SamplingInterval"))
                {
                    if (value < 0.1 || value > 60)
                    {
                        errorMessage = "采样间隔应在 0.1-60 秒之间";
                        return false;
                    }
                }
                else if (parameterName.Contains("Duration"))
                {
                    if (value < 1 || value > 999999)
                    {
                        errorMessage = "持续时间应在 1-999999 秒之间";
                        return false;
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("参数验证", $"验证参数 {parameterName} 范围失败", ex);
                errorMessage = "验证失败";
                return false;
            }
        }

        #endregion
    }

    #region Value Converters - 关键修正：补全所有必需的转换器类

    public class BoolToStrikethroughConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return value is bool b && !b ? TextDecorations.Strikethrough : null;
        }
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture) => throw new NotImplementedException();
    }

    public class BoolToButtonContentConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return value is bool b && b ? "禁用" : "启用";
        }
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture) => throw new NotImplementedException();
    }

    public class BoolToVisibilityConverter : IValueConverter
    {
        public bool Invert { get; set; }
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            bool bValue = value is bool b && b;
            if (Invert) bValue = !bValue;
            return bValue ? Visibility.Visible : Visibility.Collapsed;
            
        }
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture) => throw new NotImplementedException();
    }
    #endregion
}
﻿#pragma checksum "..\..\..\MainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "16AB82738A68CE141344A46674BC6ECC9D3FD1B9"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using LiveCharts.Wpf;
using PEMTestSystem;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace PEMTestSystem {
    
    
    /// <summary>
    /// MainWindow
    /// </summary>
    public partial class MainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 394 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock VoltageValueText;
        
        #line default
        #line hidden
        
        
        #line 400 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrentValueText;
        
        #line default
        #line hidden
        
        
        #line 406 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TemperatureValueText;
        
        #line default
        #line hidden
        
        
        #line 412 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FlowRate1ValueText;
        
        #line default
        #line hidden
        
        
        #line 418 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FlowRate2ValueText;
        
        #line default
        #line hidden
        
        
        #line 428 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ChartTitleText;
        
        #line default
        #line hidden
        
        
        #line 439 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal LiveCharts.Wpf.CartesianChart RealTimeChart;
        
        #line default
        #line hidden
        
        
        #line 466 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StartButton;
        
        #line default
        #line hidden
        
        
        #line 469 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StopButton;
        
        #line default
        #line hidden
        
        
        #line 589 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border PowerDeviceRow;
        
        #line default
        #line hidden
        
        
        #line 599 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Ellipse PowerStatusIndicator;
        
        #line default
        #line hidden
        
        
        #line 608 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PowerSettingsButton;
        
        #line default
        #line hidden
        
        
        #line 637 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border TemperatureControllerRow;
        
        #line default
        #line hidden
        
        
        #line 647 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Ellipse TemperatureStatusIndicator;
        
        #line default
        #line hidden
        
        
        #line 656 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TemperatureDisableButton;
        
        #line default
        #line hidden
        
        
        #line 661 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TemperatureSettingsButton;
        
        #line default
        #line hidden
        
        
        #line 698 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border FlowPump1Row;
        
        #line default
        #line hidden
        
        
        #line 708 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Ellipse FlowPump1StatusIndicator;
        
        #line default
        #line hidden
        
        
        #line 717 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button FlowPump1DisableButton;
        
        #line default
        #line hidden
        
        
        #line 722 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button FlowPump1SettingsButton;
        
        #line default
        #line hidden
        
        
        #line 759 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border FlowPump2Row;
        
        #line default
        #line hidden
        
        
        #line 769 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Ellipse FlowPump2StatusIndicator;
        
        #line default
        #line hidden
        
        
        #line 778 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button FlowPump2DisableButton;
        
        #line default
        #line hidden
        
        
        #line 783 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button FlowPump2SettingsButton;
        
        #line default
        #line hidden
        
        
        #line 820 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border DatabaseRow;
        
        #line default
        #line hidden
        
        
        #line 830 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Ellipse DatabaseStatusIndicator;
        
        #line default
        #line hidden
        
        
        #line 839 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DatabaseSettingsButton;
        
        #line default
        #line hidden
        
        
        #line 889 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TabControl ExperimentModeTabControl;
        
        #line default
        #line hidden
        
        
        #line 911 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ConstantCurrentTargetTemperatureBox;
        
        #line default
        #line hidden
        
        
        #line 913 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ConstantCurrentFlowPump1Box;
        
        #line default
        #line hidden
        
        
        #line 915 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ConstantCurrentFlowPump2Box;
        
        #line default
        #line hidden
        
        
        #line 917 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ConstantCurrentRepeatCountBox;
        
        #line default
        #line hidden
        
        
        #line 919 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ConstantCurrentSamplingIntervalBox;
        
        #line default
        #line hidden
        
        
        #line 935 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ConstantCurrentTargetCurrentBox;
        
        #line default
        #line hidden
        
        
        #line 937 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ConstantCurrentDurationBox;
        
        #line default
        #line hidden
        
        
        #line 939 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ConstantCurrentVoltageUpperLimitBox;
        
        #line default
        #line hidden
        
        
        #line 941 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ConstantCurrentVoltageLowerLimitBox;
        
        #line default
        #line hidden
        
        
        #line 964 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ConstantVoltageTargetTemperatureBox;
        
        #line default
        #line hidden
        
        
        #line 966 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ConstantVoltageFlowPump1Box;
        
        #line default
        #line hidden
        
        
        #line 968 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ConstantVoltageFlowPump2Box;
        
        #line default
        #line hidden
        
        
        #line 970 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ConstantVoltageRepeatCountBox;
        
        #line default
        #line hidden
        
        
        #line 972 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ConstantVoltageSamplingIntervalBox;
        
        #line default
        #line hidden
        
        
        #line 988 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ConstantVoltageTargetVoltageBox;
        
        #line default
        #line hidden
        
        
        #line 990 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ConstantVoltageDurationBox;
        
        #line default
        #line hidden
        
        
        #line 992 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ConstantVoltageCurrentUpperLimitBox;
        
        #line default
        #line hidden
        
        
        #line 994 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ConstantVoltageCurrentLowerLimitBox;
        
        #line default
        #line hidden
        
        
        #line 1017 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LinearScanTargetTemperatureBox;
        
        #line default
        #line hidden
        
        
        #line 1019 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LinearScanFlowPump1Box;
        
        #line default
        #line hidden
        
        
        #line 1021 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LinearScanFlowPump2Box;
        
        #line default
        #line hidden
        
        
        #line 1023 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LinearScanRepeatCountBox;
        
        #line default
        #line hidden
        
        
        #line 1025 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LinearScanSamplingIntervalBox;
        
        #line default
        #line hidden
        
        
        #line 1045 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LinearScanStartVoltageBox;
        
        #line default
        #line hidden
        
        
        #line 1047 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LinearScanEndVoltageBox;
        
        #line default
        #line hidden
        
        
        #line 1050 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton ScanByRateRadio;
        
        #line default
        #line hidden
        
        
        #line 1051 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton ScanByTimeRadio;
        
        #line default
        #line hidden
        
        
        #line 1054 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid ScanRateGroup;
        
        #line default
        #line hidden
        
        
        #line 1060 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LinearScanRateBox;
        
        #line default
        #line hidden
        
        
        #line 1062 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid ScanTimeGroup;
        
        #line default
        #line hidden
        
        
        #line 1068 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LinearScanTimeBox;
        
        #line default
        #line hidden
        
        
        #line 1081 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LinearScanCurrentUpperLimitBox;
        
        #line default
        #line hidden
        
        
        #line 1083 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LinearScanCurrentLowerLimitBox;
        
        #line default
        #line hidden
        
        
        #line 1085 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LinearScanHoldTimeBox;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/PEMTestSystem;component/mainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\MainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.VoltageValueText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.CurrentValueText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.TemperatureValueText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.FlowRate1ValueText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.FlowRate2ValueText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.ChartTitleText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            
            #line 432 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ExportChart_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            
            #line 434 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ShowHistory_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.RealTimeChart = ((LiveCharts.Wpf.CartesianChart)(target));
            return;
            case 10:
            this.StartButton = ((System.Windows.Controls.Button)(target));
            
            #line 467 "..\..\..\MainWindow.xaml"
            this.StartButton.Click += new System.Windows.RoutedEventHandler(this.StartButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.StopButton = ((System.Windows.Controls.Button)(target));
            
            #line 470 "..\..\..\MainWindow.xaml"
            this.StopButton.Click += new System.Windows.RoutedEventHandler(this.StopButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            
            #line 485 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeviceSettingsButton_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.PowerDeviceRow = ((System.Windows.Controls.Border)(target));
            return;
            case 16:
            this.PowerStatusIndicator = ((System.Windows.Shapes.Ellipse)(target));
            return;
            case 17:
            this.PowerSettingsButton = ((System.Windows.Controls.Button)(target));
            
            #line 610 "..\..\..\MainWindow.xaml"
            this.PowerSettingsButton.Click += new System.Windows.RoutedEventHandler(this.PowerSettingsButton_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.TemperatureControllerRow = ((System.Windows.Controls.Border)(target));
            return;
            case 19:
            this.TemperatureStatusIndicator = ((System.Windows.Shapes.Ellipse)(target));
            return;
            case 20:
            this.TemperatureDisableButton = ((System.Windows.Controls.Button)(target));
            
            #line 658 "..\..\..\MainWindow.xaml"
            this.TemperatureDisableButton.Click += new System.Windows.RoutedEventHandler(this.TemperatureDisableButton_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.TemperatureSettingsButton = ((System.Windows.Controls.Button)(target));
            
            #line 663 "..\..\..\MainWindow.xaml"
            this.TemperatureSettingsButton.Click += new System.Windows.RoutedEventHandler(this.TemperatureSettingsButton_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            this.FlowPump1Row = ((System.Windows.Controls.Border)(target));
            return;
            case 23:
            this.FlowPump1StatusIndicator = ((System.Windows.Shapes.Ellipse)(target));
            return;
            case 24:
            this.FlowPump1DisableButton = ((System.Windows.Controls.Button)(target));
            
            #line 719 "..\..\..\MainWindow.xaml"
            this.FlowPump1DisableButton.Click += new System.Windows.RoutedEventHandler(this.FlowPump1DisableButton_Click);
            
            #line default
            #line hidden
            return;
            case 25:
            this.FlowPump1SettingsButton = ((System.Windows.Controls.Button)(target));
            
            #line 724 "..\..\..\MainWindow.xaml"
            this.FlowPump1SettingsButton.Click += new System.Windows.RoutedEventHandler(this.FlowPump1SettingsButton_Click);
            
            #line default
            #line hidden
            return;
            case 26:
            this.FlowPump2Row = ((System.Windows.Controls.Border)(target));
            return;
            case 27:
            this.FlowPump2StatusIndicator = ((System.Windows.Shapes.Ellipse)(target));
            return;
            case 28:
            this.FlowPump2DisableButton = ((System.Windows.Controls.Button)(target));
            
            #line 780 "..\..\..\MainWindow.xaml"
            this.FlowPump2DisableButton.Click += new System.Windows.RoutedEventHandler(this.FlowPump2DisableButton_Click);
            
            #line default
            #line hidden
            return;
            case 29:
            this.FlowPump2SettingsButton = ((System.Windows.Controls.Button)(target));
            
            #line 785 "..\..\..\MainWindow.xaml"
            this.FlowPump2SettingsButton.Click += new System.Windows.RoutedEventHandler(this.FlowPump2SettingsButton_Click);
            
            #line default
            #line hidden
            return;
            case 30:
            this.DatabaseRow = ((System.Windows.Controls.Border)(target));
            return;
            case 31:
            this.DatabaseStatusIndicator = ((System.Windows.Shapes.Ellipse)(target));
            return;
            case 32:
            this.DatabaseSettingsButton = ((System.Windows.Controls.Button)(target));
            
            #line 841 "..\..\..\MainWindow.xaml"
            this.DatabaseSettingsButton.Click += new System.Windows.RoutedEventHandler(this.DatabaseSettingsButton_Click);
            
            #line default
            #line hidden
            return;
            case 33:
            
            #line 877 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SelectConfigButton_Click);
            
            #line default
            #line hidden
            return;
            case 34:
            
            #line 879 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveConfigButton_Click);
            
            #line default
            #line hidden
            return;
            case 35:
            this.ExperimentModeTabControl = ((System.Windows.Controls.TabControl)(target));
            
            #line 892 "..\..\..\MainWindow.xaml"
            this.ExperimentModeTabControl.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.ExperimentModeTabControl_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 36:
            this.ConstantCurrentTargetTemperatureBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 911 "..\..\..\MainWindow.xaml"
            this.ConstantCurrentTargetTemperatureBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ParameterTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 37:
            this.ConstantCurrentFlowPump1Box = ((System.Windows.Controls.TextBox)(target));
            
            #line 913 "..\..\..\MainWindow.xaml"
            this.ConstantCurrentFlowPump1Box.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ParameterTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 38:
            this.ConstantCurrentFlowPump2Box = ((System.Windows.Controls.TextBox)(target));
            
            #line 915 "..\..\..\MainWindow.xaml"
            this.ConstantCurrentFlowPump2Box.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ParameterTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 39:
            this.ConstantCurrentRepeatCountBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 917 "..\..\..\MainWindow.xaml"
            this.ConstantCurrentRepeatCountBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ParameterTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 40:
            this.ConstantCurrentSamplingIntervalBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 919 "..\..\..\MainWindow.xaml"
            this.ConstantCurrentSamplingIntervalBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ParameterTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 41:
            this.ConstantCurrentTargetCurrentBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 935 "..\..\..\MainWindow.xaml"
            this.ConstantCurrentTargetCurrentBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ParameterTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 42:
            this.ConstantCurrentDurationBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 937 "..\..\..\MainWindow.xaml"
            this.ConstantCurrentDurationBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ParameterTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 43:
            this.ConstantCurrentVoltageUpperLimitBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 939 "..\..\..\MainWindow.xaml"
            this.ConstantCurrentVoltageUpperLimitBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ParameterTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 44:
            this.ConstantCurrentVoltageLowerLimitBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 941 "..\..\..\MainWindow.xaml"
            this.ConstantCurrentVoltageLowerLimitBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ParameterTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 45:
            this.ConstantVoltageTargetTemperatureBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 964 "..\..\..\MainWindow.xaml"
            this.ConstantVoltageTargetTemperatureBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ParameterTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 46:
            this.ConstantVoltageFlowPump1Box = ((System.Windows.Controls.TextBox)(target));
            
            #line 966 "..\..\..\MainWindow.xaml"
            this.ConstantVoltageFlowPump1Box.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ParameterTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 47:
            this.ConstantVoltageFlowPump2Box = ((System.Windows.Controls.TextBox)(target));
            
            #line 968 "..\..\..\MainWindow.xaml"
            this.ConstantVoltageFlowPump2Box.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ParameterTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 48:
            this.ConstantVoltageRepeatCountBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 970 "..\..\..\MainWindow.xaml"
            this.ConstantVoltageRepeatCountBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ParameterTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 49:
            this.ConstantVoltageSamplingIntervalBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 972 "..\..\..\MainWindow.xaml"
            this.ConstantVoltageSamplingIntervalBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ParameterTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 50:
            this.ConstantVoltageTargetVoltageBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 988 "..\..\..\MainWindow.xaml"
            this.ConstantVoltageTargetVoltageBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ParameterTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 51:
            this.ConstantVoltageDurationBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 990 "..\..\..\MainWindow.xaml"
            this.ConstantVoltageDurationBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ParameterTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 52:
            this.ConstantVoltageCurrentUpperLimitBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 992 "..\..\..\MainWindow.xaml"
            this.ConstantVoltageCurrentUpperLimitBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ParameterTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 53:
            this.ConstantVoltageCurrentLowerLimitBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 994 "..\..\..\MainWindow.xaml"
            this.ConstantVoltageCurrentLowerLimitBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ParameterTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 54:
            this.LinearScanTargetTemperatureBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 1017 "..\..\..\MainWindow.xaml"
            this.LinearScanTargetTemperatureBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ParameterTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 55:
            this.LinearScanFlowPump1Box = ((System.Windows.Controls.TextBox)(target));
            
            #line 1019 "..\..\..\MainWindow.xaml"
            this.LinearScanFlowPump1Box.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ParameterTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 56:
            this.LinearScanFlowPump2Box = ((System.Windows.Controls.TextBox)(target));
            
            #line 1021 "..\..\..\MainWindow.xaml"
            this.LinearScanFlowPump2Box.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ParameterTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 57:
            this.LinearScanRepeatCountBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 1023 "..\..\..\MainWindow.xaml"
            this.LinearScanRepeatCountBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ParameterTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 58:
            this.LinearScanSamplingIntervalBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 1025 "..\..\..\MainWindow.xaml"
            this.LinearScanSamplingIntervalBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ParameterTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 59:
            this.LinearScanStartVoltageBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 1045 "..\..\..\MainWindow.xaml"
            this.LinearScanStartVoltageBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ParameterTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 60:
            this.LinearScanEndVoltageBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 1047 "..\..\..\MainWindow.xaml"
            this.LinearScanEndVoltageBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ParameterTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 61:
            this.ScanByRateRadio = ((System.Windows.Controls.RadioButton)(target));
            
            #line 1050 "..\..\..\MainWindow.xaml"
            this.ScanByRateRadio.Checked += new System.Windows.RoutedEventHandler(this.ScanMethod_Checked);
            
            #line default
            #line hidden
            return;
            case 62:
            this.ScanByTimeRadio = ((System.Windows.Controls.RadioButton)(target));
            
            #line 1051 "..\..\..\MainWindow.xaml"
            this.ScanByTimeRadio.Checked += new System.Windows.RoutedEventHandler(this.ScanMethod_Checked);
            
            #line default
            #line hidden
            return;
            case 63:
            this.ScanRateGroup = ((System.Windows.Controls.Grid)(target));
            return;
            case 64:
            this.LinearScanRateBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 1060 "..\..\..\MainWindow.xaml"
            this.LinearScanRateBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ParameterTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 65:
            this.ScanTimeGroup = ((System.Windows.Controls.Grid)(target));
            return;
            case 66:
            this.LinearScanTimeBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 1068 "..\..\..\MainWindow.xaml"
            this.LinearScanTimeBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ParameterTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 67:
            this.LinearScanCurrentUpperLimitBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 1081 "..\..\..\MainWindow.xaml"
            this.LinearScanCurrentUpperLimitBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ParameterTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 68:
            this.LinearScanCurrentLowerLimitBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 1083 "..\..\..\MainWindow.xaml"
            this.LinearScanCurrentLowerLimitBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ParameterTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 69:
            this.LinearScanHoldTimeBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 1085 "..\..\..\MainWindow.xaml"
            this.LinearScanHoldTimeBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.ParameterTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 13:
            
            #line 535 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeviceSettingsButton_Click);
            
            #line default
            #line hidden
            break;
            case 14:
            
            #line 545 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ToggleDeviceState_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}


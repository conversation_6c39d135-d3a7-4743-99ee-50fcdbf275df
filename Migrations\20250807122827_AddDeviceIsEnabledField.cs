﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PEMTestSystem.Migrations
{
    /// <inheritdoc />
    public partial class AddDeviceIsEnabledField : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "AlarmRecords",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Type = table.Column<string>(type: "nvarchar(450)", nullable: false),
                    Message = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Level = table.Column<int>(type: "int", nullable: false),
                    CreateTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ConfirmTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ConfirmUser = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    IsConfirmed = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AlarmRecords", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Devices",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    DeviceType = table.Column<int>(type: "int", nullable: false),
                    DeviceId = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    DeviceName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Model = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    ConnectionType = table.Column<int>(type: "int", nullable: false),
                    ConnectionString = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: false),
                    Specifications = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    IsEnabled = table.Column<bool>(type: "bit", nullable: false),
                    LastCalibrationDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Devices", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Experiments",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    ExperimentType = table.Column<int>(type: "int", nullable: false),
                    Status = table.Column<int>(type: "int", nullable: false),
                    RequestTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeviceReadyTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ExperimentStartTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ExperimentEndTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Configuration = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    TotalDataPoints = table.Column<long>(type: "bigint", nullable: false),
                    PlannedDuration = table.Column<int>(type: "int", nullable: true),
                    ActualDuration = table.Column<int>(type: "int", nullable: true),
                    PreheatingDuration = table.Column<int>(type: "int", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETDATE()"),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETDATE()")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Experiments", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "ExperimentTemplates",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    ExperimentType = table.Column<int>(type: "int", nullable: false),
                    Configuration = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    IsDefault = table.Column<bool>(type: "bit", nullable: false),
                    IsSystem = table.Column<bool>(type: "bit", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ExperimentTemplates", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "SystemConfigurations",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ConfigurationKey = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    ConfigurationValue = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    Category = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    DataType = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    IsEncrypted = table.Column<bool>(type: "bit", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SystemConfigurations", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "DataExports",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ExperimentId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ExportName = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    ExportFormat = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    TimeFormat = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    StartTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    EndTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IncludeParameterChanges = table.Column<bool>(type: "bit", nullable: false),
                    IncludeDeviceStatus = table.Column<bool>(type: "bit", nullable: false),
                    SelectedColumns = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    FilePath = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    FileSize = table.Column<long>(type: "bigint", nullable: true),
                    RecordCount = table.Column<long>(type: "bigint", nullable: true),
                    Status = table.Column<int>(type: "int", nullable: false),
                    ErrorMessage = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CompletedAt = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DataExports", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DataExports_Experiments_ExperimentId",
                        column: x => x.ExperimentId,
                        principalTable: "Experiments",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "DataPoints",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ExperimentId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Timestamp = table.Column<DateTime>(type: "datetime2(3)", nullable: false),
                    ElapsedSeconds = table.Column<decimal>(type: "decimal(18,3)", nullable: false),
                    Voltage = table.Column<decimal>(type: "decimal(10,3)", nullable: false),
                    Current = table.Column<decimal>(type: "decimal(10,2)", nullable: false),
                    Temperature = table.Column<decimal>(type: "decimal(5,1)", nullable: false),
                    FlowRate1 = table.Column<decimal>(type: "decimal(8,1)", nullable: false),
                    FlowRate2 = table.Column<decimal>(type: "decimal(8,1)", nullable: false),
                    Quality = table.Column<int>(type: "int", nullable: false),
                    Notes = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DataPoints", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DataPoints_Experiments_ExperimentId",
                        column: x => x.ExperimentId,
                        principalTable: "Experiments",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "DeviceStatuses",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    DeviceId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ExperimentId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    Status = table.Column<int>(type: "int", nullable: false),
                    StatusDetails = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CurrentParameters = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ResponseTime = table.Column<int>(type: "int", nullable: true),
                    LastSuccessfulCommunication = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Timestamp = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DeviceId1 = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    ExperimentId1 = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DeviceStatuses", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DeviceStatuses_Devices_DeviceId",
                        column: x => x.DeviceId,
                        principalTable: "Devices",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_DeviceStatuses_Devices_DeviceId1",
                        column: x => x.DeviceId1,
                        principalTable: "Devices",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_DeviceStatuses_Experiments_ExperimentId",
                        column: x => x.ExperimentId,
                        principalTable: "Experiments",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_DeviceStatuses_Experiments_ExperimentId1",
                        column: x => x.ExperimentId1,
                        principalTable: "Experiments",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "ExperimentStates",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ExperimentId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    CurrentStatus = table.Column<int>(type: "int", nullable: false),
                    CurrentCycle = table.Column<int>(type: "int", nullable: false),
                    TotalCycles = table.Column<int>(type: "int", nullable: false),
                    CurrentProgress = table.Column<decimal>(type: "decimal(5,2)", nullable: false),
                    Configuration = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    DeviceStates = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    StartTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeviceReadyTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ExperimentStartTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastSaveTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CanRecover = table.Column<bool>(type: "bit", nullable: false),
                    RecoveryNotes = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ExperimentStates", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ExperimentStates_Experiments_ExperimentId",
                        column: x => x.ExperimentId,
                        principalTable: "Experiments",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ParameterChanges",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    ExperimentId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ParameterName = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    ParameterCategory = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    OldValue = table.Column<decimal>(type: "decimal(18,6)", nullable: false),
                    NewValue = table.Column<decimal>(type: "decimal(18,6)", nullable: false),
                    Unit = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    ChangeReason = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    ChangeDescription = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    Timestamp = table.Column<DateTime>(type: "datetime2", nullable: false),
                    ElapsedSeconds = table.Column<decimal>(type: "decimal(18,3)", nullable: false),
                    ChangeSource = table.Column<int>(type: "int", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ParameterChanges", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ParameterChanges_Experiments_ExperimentId",
                        column: x => x.ExperimentId,
                        principalTable: "Experiments",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "SecurityEvents",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    EventType = table.Column<int>(type: "int", nullable: false),
                    Severity = table.Column<int>(type: "int", nullable: false),
                    EventTitle = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    EventDescription = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ExperimentId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    DeviceId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    ActionTaken = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    IsResolved = table.Column<bool>(type: "bit", nullable: false),
                    ResolvedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    SystemStateSnapshot = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Timestamp = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SecurityEvents", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SecurityEvents_Devices_DeviceId",
                        column: x => x.DeviceId,
                        principalTable: "Devices",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_SecurityEvents_Experiments_ExperimentId",
                        column: x => x.ExperimentId,
                        principalTable: "Experiments",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "SystemLogs",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Level = table.Column<int>(type: "int", nullable: false),
                    Message = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Exception = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Source = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: true),
                    Category = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    ExperimentId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    DeviceId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    Properties = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Timestamp = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SystemLogs", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SystemLogs_Devices_DeviceId",
                        column: x => x.DeviceId,
                        principalTable: "Devices",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_SystemLogs_Experiments_ExperimentId",
                        column: x => x.ExperimentId,
                        principalTable: "Experiments",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_AlarmRecords_IsConfirmed_CreateTime",
                table: "AlarmRecords",
                columns: new[] { "IsConfirmed", "CreateTime" });

            migrationBuilder.CreateIndex(
                name: "IX_AlarmRecords_Level_CreateTime",
                table: "AlarmRecords",
                columns: new[] { "Level", "CreateTime" })
                .Annotation("SqlServer:Include", new[] { "Type", "IsConfirmed" });

            migrationBuilder.CreateIndex(
                name: "IX_AlarmRecords_Type",
                table: "AlarmRecords",
                column: "Type");

            migrationBuilder.CreateIndex(
                name: "IX_DataExports_ExperimentId",
                table: "DataExports",
                column: "ExperimentId");

            migrationBuilder.CreateIndex(
                name: "IX_DataPoints_ExperimentId_ElapsedSeconds",
                table: "DataPoints",
                columns: new[] { "ExperimentId", "ElapsedSeconds" });

            migrationBuilder.CreateIndex(
                name: "IX_DataPoints_ExperimentId_Timestamp_Covering",
                table: "DataPoints",
                columns: new[] { "ExperimentId", "Timestamp" })
                .Annotation("SqlServer:Include", new[] { "Voltage", "Current", "Temperature", "FlowRate1", "FlowRate2", "ElapsedSeconds" });

            migrationBuilder.CreateIndex(
                name: "IX_DataPoints_Quality_Timestamp",
                table: "DataPoints",
                columns: new[] { "Quality", "Timestamp" });

            migrationBuilder.CreateIndex(
                name: "IX_Devices_Type",
                table: "Devices",
                column: "DeviceType");

            migrationBuilder.CreateIndex(
                name: "UX_Devices_DeviceId",
                table: "Devices",
                column: "DeviceId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_DeviceStatus_DeviceId_Timestamp",
                table: "DeviceStatuses",
                columns: new[] { "DeviceId", "Timestamp" });

            migrationBuilder.CreateIndex(
                name: "IX_DeviceStatuses_DeviceId1",
                table: "DeviceStatuses",
                column: "DeviceId1");

            migrationBuilder.CreateIndex(
                name: "IX_DeviceStatuses_ExperimentId",
                table: "DeviceStatuses",
                column: "ExperimentId");

            migrationBuilder.CreateIndex(
                name: "IX_DeviceStatuses_ExperimentId1",
                table: "DeviceStatuses",
                column: "ExperimentId1");

            migrationBuilder.CreateIndex(
                name: "IX_Experiments_StartTime",
                table: "Experiments",
                column: "ExperimentStartTime");

            migrationBuilder.CreateIndex(
                name: "IX_Experiments_Type_Status",
                table: "Experiments",
                columns: new[] { "ExperimentType", "Status" });

            migrationBuilder.CreateIndex(
                name: "IX_ExperimentStates_ExperimentId",
                table: "ExperimentStates",
                column: "ExperimentId");

            migrationBuilder.CreateIndex(
                name: "IX_ExperimentTemplates_Name",
                table: "ExperimentTemplates",
                column: "Name");

            migrationBuilder.CreateIndex(
                name: "IX_ExperimentTemplates_Type_Default",
                table: "ExperimentTemplates",
                columns: new[] { "ExperimentType", "IsDefault" });

            migrationBuilder.CreateIndex(
                name: "IX_ParameterChanges_ExperimentId_Timestamp",
                table: "ParameterChanges",
                columns: new[] { "ExperimentId", "Timestamp" });

            migrationBuilder.CreateIndex(
                name: "IX_SecurityEvents_DeviceId",
                table: "SecurityEvents",
                column: "DeviceId");

            migrationBuilder.CreateIndex(
                name: "IX_SecurityEvents_ExperimentId",
                table: "SecurityEvents",
                column: "ExperimentId");

            migrationBuilder.CreateIndex(
                name: "IX_SecurityEvents_Type_Severity",
                table: "SecurityEvents",
                columns: new[] { "EventType", "Severity", "Timestamp" });

            migrationBuilder.CreateIndex(
                name: "UX_SystemConfigurations_Key",
                table: "SystemConfigurations",
                column: "ConfigurationKey",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_SystemLogs_DeviceId",
                table: "SystemLogs",
                column: "DeviceId");

            migrationBuilder.CreateIndex(
                name: "IX_SystemLogs_ExperimentId",
                table: "SystemLogs",
                column: "ExperimentId");

            migrationBuilder.CreateIndex(
                name: "IX_SystemLogs_Level_Timestamp",
                table: "SystemLogs",
                columns: new[] { "Level", "Timestamp" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AlarmRecords");

            migrationBuilder.DropTable(
                name: "DataExports");

            migrationBuilder.DropTable(
                name: "DataPoints");

            migrationBuilder.DropTable(
                name: "DeviceStatuses");

            migrationBuilder.DropTable(
                name: "ExperimentStates");

            migrationBuilder.DropTable(
                name: "ExperimentTemplates");

            migrationBuilder.DropTable(
                name: "ParameterChanges");

            migrationBuilder.DropTable(
                name: "SecurityEvents");

            migrationBuilder.DropTable(
                name: "SystemConfigurations");

            migrationBuilder.DropTable(
                name: "SystemLogs");

            migrationBuilder.DropTable(
                name: "Devices");

            migrationBuilder.DropTable(
                name: "Experiments");
        }
    }
}

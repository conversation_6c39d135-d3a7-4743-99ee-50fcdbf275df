using System;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using PEMTestSystem.Services.Devices;
using PEMTestSystem.Data;
using Microsoft.EntityFrameworkCore;

namespace PEMTestSystem.Tests
{
    /// <summary>
    /// 设备禁用/启用功能测试类
    /// </summary>
    public class DeviceEnableDisableTests
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly DeviceManager _deviceManager;
        private readonly PEMTestDbContext _context;

        public DeviceEnableDisableTests()
        {
            // 创建测试服务容器
            var services = new ServiceCollection();
            
            // 配置内存数据库用于测试
            services.AddDbContext<PEMTestDbContext>(options =>
                options.UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString()));
            
            // 添加日志服务
            services.AddLogging(builder => builder.AddConsole());
            
            // 添加设备管理器
            services.AddSingleton<DeviceManager>();
            
            _serviceProvider = services.BuildServiceProvider();
            _deviceManager = _serviceProvider.GetRequiredService<DeviceManager>();
            _context = _serviceProvider.GetRequiredService<PEMTestDbContext>();
            
            // 初始化测试数据
            InitializeTestData().Wait();
        }

        /// <summary>
        /// 初始化测试数据
        /// </summary>
        private async Task InitializeTestData()
        {
            // 创建测试设备
            var testDevice = new Models.Devices.Device
            {
                Id = Guid.NewGuid(),
                DeviceId = "TestDevice_01",
                DeviceName = "测试设备1",
                Model = "TestModel",
                DeviceType = Models.Devices.DeviceType.TemperatureController,
                ConnectionType = Models.Devices.ConnectionType.ModbusRTU,
                ConnectionString = "{\"Port\":\"COM1\",\"BaudRate\":9600,\"Address\":1}",
                IsActive = true,
                IsEnabled = true,
                CreatedAt = DateTime.Now,
                UpdatedAt = DateTime.Now
            };

            _context.Devices.Add(testDevice);
            await _context.SaveChangesAsync();
        }

        /// <summary>
        /// 测试设备禁用功能
        /// </summary>
        public async Task<bool> TestDisableDevice()
        {
            try
            {
                Console.WriteLine("开始测试设备禁用功能...");
                
                // 禁用设备
                var result = await _deviceManager.DisableDeviceAsync("TestDevice_01");
                
                if (!result)
                {
                    Console.WriteLine("❌ 设备禁用失败");
                    return false;
                }
                
                // 验证数据库中的状态
                var device = await _context.Devices
                    .FirstOrDefaultAsync(d => d.DeviceId == "TestDevice_01");
                
                if (device == null || device.IsEnabled)
                {
                    Console.WriteLine("❌ 数据库中设备状态未正确更新");
                    return false;
                }
                
                // 验证设备管理器中的状态
                var isEnabled = await _deviceManager.IsDeviceEnabledAsync("TestDevice_01");
                if (isEnabled)
                {
                    Console.WriteLine("❌ 设备管理器返回的状态不正确");
                    return false;
                }
                
                Console.WriteLine("✅ 设备禁用功能测试通过");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 设备禁用测试异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试设备启用功能
        /// </summary>
        public async Task<bool> TestEnableDevice()
        {
            try
            {
                Console.WriteLine("开始测试设备启用功能...");
                
                // 启用设备
                var result = await _deviceManager.EnableDeviceAsync("TestDevice_01");
                
                if (!result)
                {
                    Console.WriteLine("❌ 设备启用失败");
                    return false;
                }
                
                // 验证数据库中的状态
                var device = await _context.Devices
                    .FirstOrDefaultAsync(d => d.DeviceId == "TestDevice_01");
                
                if (device == null || !device.IsEnabled)
                {
                    Console.WriteLine("❌ 数据库中设备状态未正确更新");
                    return false;
                }
                
                // 验证设备管理器中的状态
                var isEnabled = await _deviceManager.IsDeviceEnabledAsync("TestDevice_01");
                if (!isEnabled)
                {
                    Console.WriteLine("❌ 设备管理器返回的状态不正确");
                    return false;
                }
                
                Console.WriteLine("✅ 设备启用功能测试通过");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 设备启用测试异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 测试获取已启用设备列表
        /// </summary>
        public async Task<bool> TestGetEnabledDevices()
        {
            try
            {
                Console.WriteLine("开始测试获取已启用设备列表...");
                
                var enabledDevices = await _deviceManager.GetEnabledDevicesAsync();
                
                if (enabledDevices.Count == 0)
                {
                    Console.WriteLine("❌ 未获取到已启用的设备");
                    return false;
                }
                
                if (!enabledDevices.Contains("TestDevice_01"))
                {
                    Console.WriteLine("❌ 测试设备不在已启用设备列表中");
                    return false;
                }
                
                Console.WriteLine($"✅ 获取已启用设备列表测试通过，共 {enabledDevices.Count} 个设备");
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 获取已启用设备列表测试异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 运行所有测试
        /// </summary>
        public async Task<bool> RunAllTests()
        {
            Console.WriteLine("=== 设备禁用/启用功能测试开始 ===");
            
            var tests = new[]
            {
                TestDisableDevice,
                TestEnableDevice,
                TestGetEnabledDevices
            };
            
            int passedTests = 0;
            int totalTests = tests.Length;
            
            foreach (var test in tests)
            {
                if (await test())
                {
                    passedTests++;
                }
                Console.WriteLine();
            }
            
            Console.WriteLine($"=== 测试结果: {passedTests}/{totalTests} 通过 ===");
            
            return passedTests == totalTests;
        }

        /// <summary>
        /// 清理资源
        /// </summary>
        public void Dispose()
        {
            _context?.Dispose();
            _serviceProvider?.GetService<IServiceScope>()?.Dispose();
        }
    }
}
